import sys
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open

# 添加backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "backend"))

from core.config import Config, LogConfig, <PERSON><PERSON><PERSON>onfig, CorsConfig


def test_config_with_real_yaml():
    """测试使用真实YAML数据的配置加载"""
    
    # 准备测试数据
    config_data = {
        "log": {
            "dir": "/export/Logs",
            "name": "app.log",
            "level": "info",
            "max_size_m": 5
        },
        "api": {
            "host": "0.0.0.0",
            "port": 8080,
            "reload": False,
            "cors": {
                "origins": [
                    "http://localhost:3000",
                    "http://localhost:8080",
                    "http://127.0.0.1:3000",
                    "http://127.0.0.1:8080"
                ],
                "allow_credentials": True,
                "allow_methods": ["*"],
                "allow_headers": ["*"]
            }
        }
    }
    
    # 直接使用配置数据创建Config对象
    config = Config(**config_data)
    
    # 验证配置
    assert isinstance(config.log, LogConfig)
    assert config.log.dir == "/export/Logs"
    assert config.log.name == "app.log"
    assert config.log.level == "info"
    assert config.log.max_size_m == 5
    
    assert isinstance(config.api, ApiConfig)
    assert config.api.host == "0.0.0.0"
    assert config.api.port == 8080
    assert config.api.reload is False
    
    assert isinstance(config.api.cors, CorsConfig)
    assert len(config.api.cors.origins) == 4
    assert "http://localhost:3000" in config.api.cors.origins
    assert config.api.cors.allow_credentials is True
    assert config.api.cors.allow_methods == ["*"]
    assert config.api.cors.allow_headers == ["*"]


def test_config_yaml_serialization():
    """测试配置的YAML序列化和反序列化"""
    
    # 创建配置对象
    log_config = LogConfig(
        dir="/tmp/logs",
        name="test.log",
        level="debug",
        max_size_m=10
    )
    
    cors_config = CorsConfig(
        origins=["http://localhost:3000"],
        allow_credentials=False,
        allow_methods=["GET", "POST"],
        allow_headers=["Content-Type"]
    )
    
    api_config = ApiConfig(
        host="127.0.0.1",
        port=9000,
        reload=True,
        cors=cors_config
    )
    
    config = Config(log=log_config, api=api_config)
    
    # 序列化为字典
    config_dict = config.model_dump()
    
    # 验证序列化结果
    assert config_dict["log"]["dir"] == "/tmp/logs"
    assert config_dict["log"]["name"] == "test.log"
    assert config_dict["log"]["level"] == "debug"
    assert config_dict["log"]["max_size_m"] == 10
    
    assert config_dict["api"]["host"] == "127.0.0.1"
    assert config_dict["api"]["port"] == 9000
    assert config_dict["api"]["reload"] is True
    
    assert config_dict["api"]["cors"]["origins"] == ["http://localhost:3000"]
    assert config_dict["api"]["cors"]["allow_credentials"] is False
    
    # 从字典重新创建配置对象
    new_config = Config(**config_dict)
    
    # 验证重新创建的配置对象
    assert new_config.log.dir == config.log.dir
    assert new_config.log.name == config.log.name
    assert new_config.api.host == config.api.host
    assert new_config.api.port == config.api.port
    assert new_config.api.cors.origins == config.api.cors.origins


def test_config_defaults():
    """测试配置的默认值"""
    
    # 只提供必需字段
    minimal_config_data = {
        "log": {
            "dir": "/tmp",
            "name": "app.log"
        },
        "api": {}
    }
    
    config = Config(**minimal_config_data)
    
    # 验证默认值
    assert config.log.level == "info"  # 默认值
    assert config.log.max_size_m == 5  # 默认值
    
    assert config.api.host == "0.0.0.0"  # 默认值
    assert config.api.port == 8080  # 默认值
    assert config.api.reload is False  # 默认值
    
    assert config.api.cors.origins == []  # 默认值
    assert config.api.cors.allow_credentials is True  # 默认值
    assert config.api.cors.allow_methods == ["*"]  # 默认值
    assert config.api.cors.allow_headers == ["*"]  # 默认值


if __name__ == "__main__":
    test_config_with_real_yaml()
    test_config_yaml_serialization()
    test_config_defaults()
    print("所有测试通过！")
