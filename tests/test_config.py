import pytest
import os
import sys
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch

# 添加backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "backend"))

from core.config import (
    Config,
    LogConfig,
    ApiConfig,
    CorsConfig,
    load_config,
    get_config,
    reload_config,
    _config
)


class TestConfigModels:
    """测试配置模型"""
    
    def test_log_config_creation(self):
        """测试日志配置创建"""
        log_config = LogConfig(
            dir="/tmp/logs",
            name="test.log",
            level="debug",
            max_size_m=10
        )
        assert log_config.dir == "/tmp/logs"
        assert log_config.name == "test.log"
        assert log_config.level == "debug"
        assert log_config.max_size_m == 10
    
    def test_log_config_defaults(self):
        """测试日志配置默认值"""
        log_config = LogConfig(dir="/tmp", name="app.log")
        assert log_config.level == "info"
        assert log_config.max_size_m == 5
    
    def test_cors_config_creation(self):
        """测试跨域配置创建"""
        cors_config = CorsConfig(
            origins=["http://localhost:3000"],
            allow_credentials=False,
            allow_methods=["GET", "POST"],
            allow_headers=["Content-Type"]
        )
        assert cors_config.origins == ["http://localhost:3000"]
        assert cors_config.allow_credentials is False
        assert cors_config.allow_methods == ["GET", "POST"]
        assert cors_config.allow_headers == ["Content-Type"]
    
    def test_cors_config_defaults(self):
        """测试跨域配置默认值"""
        cors_config = CorsConfig()
        assert cors_config.origins == []
        assert cors_config.allow_credentials is True
        assert cors_config.allow_methods == ["*"]
        assert cors_config.allow_headers == ["*"]
    
    def test_api_config_creation(self):
        """测试API配置创建"""
        cors_config = CorsConfig(origins=["http://localhost:3000"])
        api_config = ApiConfig(
            host="127.0.0.1",
            port=9000,
            reload=True,
            cors=cors_config
        )
        assert api_config.host == "127.0.0.1"
        assert api_config.port == 9000
        assert api_config.reload is True
        assert api_config.cors.origins == ["http://localhost:3000"]
    
    def test_api_config_defaults(self):
        """测试API配置默认值"""
        api_config = ApiConfig()
        assert api_config.host == "0.0.0.0"
        assert api_config.port == 8080
        assert api_config.reload is False
        assert isinstance(api_config.cors, CorsConfig)
    
    def test_main_config_creation(self):
        """测试主配置创建"""
        log_config = LogConfig(dir="/tmp", name="test.log")
        api_config = ApiConfig(host="127.0.0.1", port=9000)
        
        config = Config(log=log_config, api=api_config)
        assert isinstance(config.log, LogConfig)
        assert isinstance(config.api, ApiConfig)
        assert config.log.dir == "/tmp"
        assert config.api.host == "127.0.0.1"


class TestConfigLoading:
    """测试配置加载功能"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 清理全局配置
        global _config
        _config = None
        get_config.cache_clear()
    
    def create_temp_config_file(self, config_data: dict) -> Path:
        """创建临时配置文件"""
        temp_dir = Path(tempfile.mkdtemp())
        config_file = temp_dir / "config.yaml"
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
        
        return config_file
    
    def test_load_config_success(self):
        """测试成功加载配置"""
        config_data = {
            "log": {
                "dir": "/tmp/logs",
                "name": "test.log",
                "level": "debug",
                "max_size_m": 10
            },
            "api": {
                "host": "127.0.0.1",
                "port": 9000,
                "reload": True,
                "cors": {
                    "origins": ["http://localhost:3000"],
                    "allow_credentials": False,
                    "allow_methods": ["GET", "POST"],
                    "allow_headers": ["Content-Type"]
                }
            }
        }
        
        config_file = self.create_temp_config_file(config_data)
        
        # 模拟配置文件路径
        with patch('core.config.Path') as mock_path:
            mock_path.return_value.parent.parent = config_file.parent
            mock_path.return_value.parent.parent.__truediv__ = lambda self, x: config_file.parent
            mock_path.return_value.parent.parent.__truediv__.return_value.__truediv__ = lambda self, x: config_file
            
            # 模拟文件存在检查
            with patch.object(Path, 'exists', return_value=True):
                with patch('builtins.open', open):
                    config = load_config()
        
        assert isinstance(config, Config)
        assert config.log.dir == "/tmp/logs"
        assert config.log.name == "test.log"
        assert config.log.level == "debug"
        assert config.api.host == "127.0.0.1"
        assert config.api.port == 9000
        assert config.api.reload is True
    
    def test_load_config_file_not_found(self):
        """测试配置文件不存在的情况"""
        with patch.object(Path, 'exists', return_value=False):
            with pytest.raises(FileNotFoundError, match="配置文件不存在"):
                load_config()
    
    def test_load_config_yaml_error(self):
        """测试YAML解析错误"""
        # 创建无效的YAML文件
        temp_dir = Path(tempfile.mkdtemp())
        config_file = temp_dir / "config.yaml"
        
        with open(config_file, 'w') as f:
            f.write("invalid: yaml: content: [")
        
        with patch('core.config.Path') as mock_path:
            mock_path.return_value.parent.parent = config_file.parent
            mock_path.return_value.parent.parent.__truediv__ = lambda self, x: config_file.parent
            mock_path.return_value.parent.parent.__truediv__.return_value.__truediv__ = lambda self, x: config_file
            
            with patch.object(Path, 'exists', return_value=True):
                with pytest.raises(yaml.YAMLError, match="YAML解析错误"):
                    load_config()
    
    def test_load_config_validation_error(self):
        """测试配置验证错误"""
        config_data = {
            "log": {
                "dir": "/tmp/logs",
                "name": "test.log"
                # 缺少必需字段
            },
            "api": {
                "host": "127.0.0.1",
                "port": "invalid_port"  # 无效的端口类型
            }
        }
        
        config_file = self.create_temp_config_file(config_data)
        
        with patch('core.config.Path') as mock_path:
            mock_path.return_value.parent.parent = config_file.parent
            mock_path.return_value.parent.parent.__truediv__ = lambda self, x: config_file.parent
            mock_path.return_value.parent.parent.__truediv__.return_value.__truediv__ = lambda self, x: config_file
            
            with patch.object(Path, 'exists', return_value=True):
                with patch('builtins.open', open):
                    with pytest.raises(ValueError, match="配置验证错误"):
                        load_config()
    
    def test_get_config_caching(self):
        """测试配置缓存功能"""
        config_data = {
            "log": {"dir": "/tmp", "name": "test.log"},
            "api": {"host": "127.0.0.1", "port": 8080}
        }
        
        config_file = self.create_temp_config_file(config_data)
        
        with patch('core.config.Path') as mock_path:
            mock_path.return_value.parent.parent = config_file.parent
            mock_path.return_value.parent.parent.__truediv__ = lambda self, x: config_file.parent
            mock_path.return_value.parent.parent.__truediv__.return_value.__truediv__ = lambda self, x: config_file
            
            with patch.object(Path, 'exists', return_value=True):
                with patch('builtins.open', open):
                    config1 = get_config()
                    config2 = get_config()
        
        # 应该返回同一个实例（缓存）
        assert config1 is config2
    
    def test_reload_config(self):
        """测试重新加载配置"""
        config_data = {
            "log": {"dir": "/tmp", "name": "test.log"},
            "api": {"host": "127.0.0.1", "port": 8080}
        }
        
        config_file = self.create_temp_config_file(config_data)
        
        with patch('core.config.Path') as mock_path:
            mock_path.return_value.parent.parent = config_file.parent
            mock_path.return_value.parent.parent.__truediv__ = lambda self, x: config_file.parent
            mock_path.return_value.parent.parent.__truediv__.return_value.__truediv__ = lambda self, x: config_file
            
            with patch.object(Path, 'exists', return_value=True):
                with patch('builtins.open', open):
                    config1 = get_config()
                    config2 = reload_config()
        
        # 重新加载后应该是不同的实例
        assert config1 is not config2
        assert isinstance(config2, Config)


if __name__ == "__main__":
    pytest.main([__file__])
