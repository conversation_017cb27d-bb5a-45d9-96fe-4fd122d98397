import sys
import tempfile
import os
from pathlib import Path
import logging

# 添加backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "backend"))


def test_logger_basic_functionality():
    """测试日志基本功能"""
    
    from utils.logger import logger, info, warning, error, get_logger
    
    # 测试logger对象存在
    assert logger is not None
    assert isinstance(logger, logging.Logger)
    
    # 测试便捷函数存在且可调用
    assert callable(info)
    assert callable(warning)
    assert callable(error)
    assert callable(get_logger)
    
    # 测试便捷函数不会抛出异常
    try:
        info("测试信息日志")
        warning("测试警告日志")
        error("测试错误日志")
        print("✓ 便捷函数测试通过")
    except Exception as e:
        assert False, f"便捷函数调用失败: {e}"


def test_get_logger_function():
    """测试get_logger函数"""
    
    from utils.logger import get_logger
    
    # 测试获取命名logger
    test_logger = get_logger("test_module")
    assert test_logger.name == "test_module"
    assert isinstance(test_logger, logging.Logger)
    
    # 测试logger有处理器
    assert len(test_logger.handlers) > 0
    
    # 测试logger可以记录日志
    try:
        test_logger.info("测试来自get_logger的日志")
        print("✓ get_logger函数测试通过")
    except Exception as e:
        assert False, f"get_logger创建的logger记录日志失败: {e}"


def test_log_file_creation():
    """测试日志文件创建"""
    
    # 导入logger会触发日志文件创建
    from utils.logger import logger, log_config
    
    # 检查日志目录是否存在
    log_dir = Path(log_config.dir)
    assert log_dir.exists(), f"日志目录不存在: {log_dir}"
    
    # 检查日志文件是否存在
    log_file = log_dir / log_config.name
    assert log_file.exists(), f"日志文件不存在: {log_file}"
    
    print(f"✓ 日志文件创建测试通过: {log_file}")


def test_log_levels():
    """测试日志级别"""
    
    from utils.logger import logger, log_level_map
    
    # 测试日志级别映射
    assert 'debug' in log_level_map
    assert 'info' in log_level_map
    assert 'warning' in log_level_map
    assert 'error' in log_level_map
    assert 'critical' in log_level_map
    
    # 测试logger有正确的级别设置
    assert hasattr(logger, 'level')
    assert logger.level in [logging.DEBUG, logging.INFO, logging.WARNING, logging.ERROR, logging.CRITICAL]
    
    print("✓ 日志级别测试通过")


def test_rotating_file_handler_exists():
    """测试RotatingFileHandler是否正确配置"""
    
    from utils.logger import file_handler
    from logging.handlers import RotatingFileHandler
    
    # 验证文件处理器类型
    assert isinstance(file_handler, RotatingFileHandler)
    
    # 验证处理器配置
    assert file_handler.maxBytes > 0
    assert file_handler.backupCount > 0
    
    print("✓ RotatingFileHandler配置测试通过")


def test_console_handler_exists():
    """测试控制台处理器是否存在"""
    
    from utils.logger import console_handler
    from rich.logging import RichHandler
    
    # 验证控制台处理器类型
    assert isinstance(console_handler, RichHandler)
    
    print("✓ 控制台处理器测试通过")


def test_logger_configuration_values():
    """测试日志配置值"""
    
    from utils.logger import log_config
    
    # 验证配置对象存在
    assert log_config is not None
    
    # 验证配置属性存在
    assert hasattr(log_config, 'dir')
    assert hasattr(log_config, 'name')
    assert hasattr(log_config, 'level')
    assert hasattr(log_config, 'max_size_m')
    
    # 验证配置值类型
    assert isinstance(log_config.dir, str)
    assert isinstance(log_config.name, str)
    assert isinstance(log_config.level, str)
    assert isinstance(log_config.max_size_m, int)
    
    print(f"✓ 日志配置测试通过: {log_config.dir}/{log_config.name}")


def test_logger_handlers():
    """测试logger处理器配置"""
    
    from utils.logger import logger
    
    # 验证logger有处理器
    assert len(logger.handlers) > 0
    
    # 验证logger不传播到根logger
    assert logger.propagate is False
    
    print(f"✓ Logger处理器测试通过，共有 {len(logger.handlers)} 个处理器")


def test_actual_logging():
    """测试实际的日志记录功能"""
    
    from utils.logger import logger
    
    # 记录不同级别的日志
    try:
        logger.debug("Debug级别日志测试")
        logger.info("Info级别日志测试")
        logger.warning("Warning级别日志测试")
        logger.error("Error级别日志测试")
        logger.critical("Critical级别日志测试")
        
        print("✓ 实际日志记录测试通过")
    except Exception as e:
        assert False, f"日志记录失败: {e}"


if __name__ == "__main__":
    test_logger_basic_functionality()
    test_get_logger_function()
    test_log_file_creation()
    test_log_levels()
    test_rotating_file_handler_exists()
    test_console_handler_exists()
    test_logger_configuration_values()
    test_logger_handlers()
    test_actual_logging()
    print("所有日志功能测试通过！")
