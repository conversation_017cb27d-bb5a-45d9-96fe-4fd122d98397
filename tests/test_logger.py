import sys
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
import logging

# 添加backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "backend"))


def test_logger_configuration():
    """测试日志配置是否正确应用"""
    
    # 模拟配置
    mock_config = MagicMock()
    mock_config.log.dir = "./test_logs"
    mock_config.log.name = "test.log"
    mock_config.log.level = "debug"
    mock_config.log.max_size_m = 1
    
    with patch('utils.logger.get_config', return_value=mock_config):
        # 重新导入logger模块以应用新配置
        import importlib
        import utils.logger
        importlib.reload(utils.logger)
        
        # 验证配置是否正确应用
        assert utils.logger.log_config.level == "debug"
        assert utils.logger.log_config.max_size_m == 1
        assert utils.logger.log_level == logging.DEBUG
        
        # 验证日志目录是否创建
        log_dir = Path("./test_logs")
        assert log_dir.exists()
        
        # 清理测试目录
        import shutil
        if log_dir.exists():
            shutil.rmtree(log_dir)


def test_logger_functions():
    """测试日志记录函数"""
    
    from utils.logger import debug, info, warning, error, critical, get_logger
    
    # 测试便捷函数（这些不会抛出异常）
    debug("测试debug日志")
    info("测试info日志")
    warning("测试warning日志")
    error("测试error日志")
    critical("测试critical日志")
    
    # 测试get_logger函数
    test_logger = get_logger("test_module")
    assert test_logger.name == "test_module"
    assert isinstance(test_logger, logging.Logger)
    
    # 测试logger有正确的处理器
    assert len(test_logger.handlers) > 0


def test_logger_fallback_config():
    """测试配置加载失败时的回退机制"""
    
    with patch('utils.logger.get_config', side_effect=Exception("配置加载失败")):
        # 重新导入logger模块
        import importlib
        import utils.logger
        importlib.reload(utils.logger)
        
        # 验证使用了默认配置
        assert utils.logger.log_config.dir == "./logs"
        assert utils.logger.log_config.name == "app.log"
        assert utils.logger.log_config.level == "info"
        assert utils.logger.log_config.max_size_m == 5


def test_log_level_mapping():
    """测试日志级别映射"""
    
    from utils.logger import log_level_map
    import logging
    
    assert log_level_map['debug'] == logging.DEBUG
    assert log_level_map['info'] == logging.INFO
    assert log_level_map['warning'] == logging.WARNING
    assert log_level_map['error'] == logging.ERROR
    assert log_level_map['critical'] == logging.CRITICAL


def test_rotating_file_handler():
    """测试日志文件轮转功能"""
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 模拟小文件大小配置以便测试轮转
        mock_config = MagicMock()
        mock_config.log.dir = temp_dir
        mock_config.log.name = "rotate_test.log"
        mock_config.log.level = "info"
        mock_config.log.max_size_m = 0.001  # 1KB，很小的文件大小
        
        with patch('utils.logger.get_config', return_value=mock_config):
            # 重新导入logger模块
            import importlib
            import utils.logger
            importlib.reload(utils.logger)
            
            # 写入大量日志以触发轮转
            for i in range(100):
                utils.logger.info(f"这是第{i}条测试日志，用于测试文件轮转功能。" * 10)
            
            # 检查是否创建了轮转文件
            log_files = list(Path(temp_dir).glob("rotate_test.log*"))
            
            # 应该至少有主日志文件
            assert len(log_files) >= 1
            
            # 验证主日志文件存在
            main_log = Path(temp_dir) / "rotate_test.log"
            assert main_log.exists()


def test_logger_with_different_levels():
    """测试不同日志级别的记录"""
    
    # 创建临时日志文件
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.log', delete=False) as temp_file:
        temp_log_path = temp_file.name
    
    try:
        # 创建测试logger
        test_logger = logging.getLogger("level_test")
        test_logger.setLevel(logging.DEBUG)
        
        # 创建文件处理器
        from logging.handlers import RotatingFileHandler
        handler = RotatingFileHandler(temp_log_path, maxBytes=1024*1024, backupCount=3)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        test_logger.addHandler(handler)
        
        # 记录不同级别的日志
        test_logger.debug("Debug message")
        test_logger.info("Info message")
        test_logger.warning("Warning message")
        test_logger.error("Error message")
        test_logger.critical("Critical message")
        
        # 刷新处理器
        handler.flush()
        
        # 读取日志文件内容
        with open(temp_log_path, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 验证所有级别的日志都被记录
        assert "Debug message" in log_content
        assert "Info message" in log_content
        assert "Warning message" in log_content
        assert "Error message" in log_content
        assert "Critical message" in log_content
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_log_path):
            os.unlink(temp_log_path)


if __name__ == "__main__":
    test_logger_configuration()
    test_logger_functions()
    test_logger_fallback_config()
    test_log_level_mapping()
    test_rotating_file_handler()
    test_logger_with_different_levels()
    print("所有日志测试通过！")
