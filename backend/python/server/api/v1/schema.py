from pydantic import BaseModel

# 数据模型
class WorkspaceInfo(BaseModel):
    id: str
    name: str
    path: str
    description: str = ""


# 搜索请求
class SearchRequest(BaseModel):
    trace_id: str
    query: str
    workspace_name: str
    search_tool: str = "term_sparse"  # 'grep' or 'term_sparse' or 'keywords'
    is_stream: bool = True

# 搜索响应
class SearchResponse(BaseModel):
    query: str
    code_snippets: str
    