# 配置系统文档

## 概述

这个配置系统基于 Pydantic 和 YAML，提供了类型安全的配置管理功能。它支持环境特定的配置文件、配置验证、缓存和热重载。

## 特性

- ✅ **类型安全**: 使用 Pydantic 模型进行配置验证
- ✅ **YAML 支持**: 支持 YAML 格式的配置文件
- ✅ **环境特定配置**: 支持不同环境的配置文件
- ✅ **配置缓存**: 使用 LRU 缓存提高性能
- ✅ **热重载**: 支持运行时重新加载配置
- ✅ **默认值**: 为所有配置项提供合理的默认值
- ✅ **扩展性**: 支持额外字段，便于未来扩展

## 配置结构

### 主要配置类

```python
class Config(BaseModel):
    """应用配置"""
    log: LogConfig      # 日志配置
    api: ApiConfig      # API配置
```

### 日志配置 (LogConfig)

```python
class LogConfig(BaseModel):
    dir: str            # 日志目录
    name: str           # 日志文件名
    level: str          # 日志级别 (默认: "info")
    max_size_m: int     # 日志文件最大大小MB (默认: 5)
```

### API配置 (ApiConfig)

```python
class ApiConfig(BaseModel):
    host: str           # 服务器主机 (默认: "0.0.0.0")
    port: int           # 服务器端口 (默认: 8080)
    reload: bool        # 是否启用热重载 (默认: False)
    cors: CorsConfig    # 跨域配置
```

### 跨域配置 (CorsConfig)

```python
class CorsConfig(BaseModel):
    origins: List[str]          # 允许的源 (默认: [])
    allow_credentials: bool     # 是否允许凭证 (默认: True)
    allow_methods: List[str]    # 允许的方法 (默认: ["*"])
    allow_headers: List[str]    # 允许的头部 (默认: ["*"])
```

## 使用方法

### 1. 基本使用

```python
from core.config import get_config

# 获取配置
config = get_config()

# 访问配置
print(f"日志目录: {config.log.dir}")
print(f"API端口: {config.api.port}")
print(f"跨域源: {config.api.cors.origins}")
```

### 2. 环境特定配置

配置系统会根据 `ENV` 环境变量加载不同的配置文件：

- `ENV=dev` → `config.dev.yaml`
- `ENV=prod` → `config.prod.yaml`
- `ENV=test` → `config.test.yaml`
- 默认 → `config.yaml`

```bash
# 使用生产环境配置
ENV=prod python app.py

# 使用测试环境配置
ENV=test python app.py
```

### 3. 配置重新加载

```python
from core.config import reload_config

# 重新加载配置
new_config = reload_config()

# 或者加载特定环境的配置
prod_config = reload_config('prod')
```

### 4. 配置验证

配置系统会自动验证配置的类型和格式：

```python
# 这会抛出 ValidationError
invalid_config = Config(
    log=LogConfig(dir="/tmp", name="app.log"),
    api=ApiConfig(port="invalid_port")  # 端口必须是整数
)
```

### 5. 配置序列化

```python
config = get_config()

# 转换为字典
config_dict = config.model_dump()

# 转换为JSON
config_json = config.model_dump_json()
```

## 配置文件示例

### config.yaml

```yaml
log:
  dir: /export/Logs
  name: app.log
  level: info
  max_size_m: 5

api:
  host: "0.0.0.0"
  port: 8080
  reload: false
  
  cors:
    origins:
      - "http://localhost:3000"
      - "http://localhost:8080"
      - "http://127.0.0.1:3000"
      - "http://127.0.0.1:8080"
    allow_credentials: true
    allow_methods: ["*"]
    allow_headers: ["*"]
```

### config.prod.yaml

```yaml
log:
  dir: /var/log/myapp
  name: app.log
  level: warning
  max_size_m: 50

api:
  host: "0.0.0.0"
  port: 80
  reload: false
  
  cors:
    origins:
      - "https://myapp.com"
      - "https://www.myapp.com"
    allow_credentials: true
    allow_methods: ["GET", "POST", "PUT", "DELETE"]
    allow_headers: ["Content-Type", "Authorization"]
```

## API 参考

### 函数

#### `get_config() -> Config`

获取配置实例（带缓存）。

**返回值**: `Config` - 配置对象

#### `load_config(env: str = None) -> Config`

加载配置文件。

**参数**:
- `env`: 环境名称，默认从 `ENV` 环境变量获取

**返回值**: `Config` - 配置对象

**异常**:
- `FileNotFoundError`: 配置文件不存在
- `yaml.YAMLError`: YAML解析错误
- `ValueError`: 配置验证错误

#### `reload_config(env: str = None) -> Config`

重新加载配置。

**参数**:
- `env`: 环境名称

**返回值**: `Config` - 新的配置对象

## 最佳实践

1. **使用 get_config()**: 在大多数情况下使用 `get_config()` 而不是直接调用 `load_config()`
2. **环境变量**: 使用 `ENV` 环境变量来切换不同环境的配置
3. **配置验证**: 依赖 Pydantic 的自动验证，不要跳过类型检查
4. **默认值**: 为所有配置项提供合理的默认值
5. **文档**: 使用 `Field(description="...")` 为配置项添加文档

## 扩展配置

要添加新的配置项，只需：

1. 在相应的配置类中添加新字段
2. 在 YAML 文件中添加对应的配置
3. 使用 `Field()` 提供默认值和描述

```python
class ApiConfig(BaseModel):
    # 现有字段...
    timeout: int = Field(default=30, description="请求超时时间(秒)")
    max_connections: int = Field(default=100, description="最大连接数")
```

## 故障排除

### 常见错误

1. **ModuleNotFoundError**: 确保 Python 路径正确
2. **FileNotFoundError**: 检查配置文件是否存在
3. **ValidationError**: 检查配置文件中的数据类型
4. **YAMLError**: 检查 YAML 文件语法

### 调试技巧

```python
# 打印配置文件路径
import os
from pathlib import Path
config_dir = Path(__file__).parent.parent / "config"
print(f"配置目录: {config_dir}")

# 打印当前环境
env = os.getenv('ENV', 'dev')
print(f"当前环境: {env}")

# 验证配置
try:
    config = get_config()
    print("配置加载成功")
except Exception as e:
    print(f"配置加载失败: {e}")
```
