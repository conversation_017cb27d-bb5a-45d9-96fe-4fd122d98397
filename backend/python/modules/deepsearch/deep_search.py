"""
DeepSearch核心模块
实现完整的深度搜索流程
"""
import re
from typing import List, Tuple
import json
import time
import asyncio

from core.config import get_config
from modules.common.constant import FileFilterMode
from modules.integrations.tools.search.any_search import get_search_tool_instance
from modules.llm.llm_client import LLMClient, default_llm_client
from modules.common.schema import SearchResult, CodeSnippet
from modules.common.constant import SearchToolEnum
from modules.deepsearch.prompts import (
    SUBQUERY_REORDER_PROMPT, 
    GENERATE_NEW_QUERY_PROMPT,
    SYSTEM_PROMPTS,
    QUERY_SPLIT_PROMPT
)
from utils.trace_logger import trace_logger
from utils.file import build_file_tree, FileNode


class DeepSearch:
    """深度搜索主类"""
    
    def __init__(
        self,
        repo_path: str,
        repo_info: str = "",
        llm_client: LLMClient = None,
        search_tool: SearchToolEnum = SearchToolEnum.ANY
    ):
        """
        初始化DeepSearch

        Args:
            repo_path: 仓库路径
            repo_info: 仓库信息描述
            llm_client: LLM客户端，如果为None则使用默认客户端
            search_type: 搜索类型
        """
        self.repo_path = repo_path
        self.repo_info = repo_info
        self.search_tool = search_tool

        self.search_instance = get_search_tool_instance(SearchToolEnum.ANY, repo_path, enabled_search_tools=[search_tool.value] if search_tool != SearchToolEnum.ANY else get_config().deepsearch.enabled_search_tools)

        self.llm_client = llm_client or default_llm_client
        self.config = get_config().deepsearch



    async def search_async(self, query: str) -> SearchResult:
        """
        执行异步深度搜索

        Args:
            query: 用户查询

        Returns:
            SearchResult: 搜索结果
        """
        result = SearchResult(original_query=query)

        trace_logger.info(f"Start Async DeepSearch for Query: {query}")

        # 获取当前仓库树结构
        repo_node = FileNode(path=self.repo_path, 
                             name=self.repo_path.split("/")[-1], 
                             type="directory", 
                             children=build_file_tree(root_dir=self.repo_path, start_dir=self.repo_path, max_leaf_nodes=100, filter_mode=FileFilterMode.LOCAL))

        # 生成新查询
        try:
            sub_queries = await self._split_query_async(
                query,
                repo_node,
                self.config.max_sub_queries
            )
        except Exception as e:
            trace_logger.error(f"生成新查询失败: {e}")
            return result

        # 将首次拆分的子查询的查询结果添加到集合中，默认使用倒排索引
        new_snippets = await self._search_and_filter_async(sub_queries, [(self.search_tool if self.search_tool != SearchToolEnum.ANY else SearchToolEnum.INVERTED_INDEX) for _ in range(len(sub_queries))])
        result.all_queries.extend(sub_queries)
        result.code_snippets.extend(new_snippets)

        new_queries = sub_queries
        # 针对每个query并行查询
        for iteration in range(self.config.max_iterations):
            if not new_queries:
                trace_logger.info("未生成新查询，搜索结束")
                break

            # 生成子查询
            queries = await self._generate_new_queries_async(query, repo_node, result.all_queries, result.code_snippets)
            result.all_queries.extend(new_queries)

            # 针对每个新查询并发执行查询
            try:
                new_snippets = await self._search_and_filter_async(queries=[query[1] for query in queries], search_tools=[query[0] for query in queries])
            except Exception as e:
                trace_logger.error(f"Iteration {iteration + 1}: Search Failed: {e}")
                break

            result.code_snippets.extend(new_snippets)
            if not new_snippets:
                # DeepSearch的方法是预设首先拆分的搜索方向没有错误，如果子查询没有找到相关代码，说明当前搜索方向已经穷尽，可以结束搜索
                trace_logger.warning(f"Iteration {iteration + 1}: Not Found Any Code Snippets")
                break

            result.iterations = iteration + 1

        # 4. 文件级别合并和去重
        trace_logger.info("Merging Code Snippets at File Level...")
        result.code_snippets = self._merge_snippets_file(result.code_snippets)[:20]

        # 5. 重排序
        trace_logger.info("Reordering Code Snippets...")
        result.code_snippets = await self._reorder_snippets_async(result.code_snippets, query, sub_queries)

        trace_logger.info(f"Async DeepSearch Completed: {result.get_summary()}")

        return result

    async def search_stream(self, query: str):
        """流式搜索"""
        try:
            result = SearchResult(original_query=query)

            trace_logger.info(f"Start DeepSearch for Query: {query}")
            yield f'data: {json.dumps({"type": "start", "message": "开始搜索", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)  # 确保数据被及时发送

            # 获取当前仓库树结构
            repo_node = FileNode(path=self.repo_path, name=self.repo_path.split("/")[-1], type="directory", children=build_file_tree(root_dir=self.repo_path, start_dir=self.repo_path, max_leaf_nodes=50, filter_mode=FileFilterMode.LOCAL))
            repo_struct = str(repo_node)

            # 生成初始查询
            yield f'data: {json.dumps({"type": "process", "message": "生成初始查询...", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)

            try:
                new_queries = await self._split_query_async(
                    query,
                    repo_struct,
                    self.config.max_sub_queries
                )
            except Exception as e:
                trace_logger.error(f"生成初始查询失败: {e}")
                yield f'data: {json.dumps({"type": "error", "message": f"生成初始查询失败: {e}", "timestamp": time.time()})}\n\n'
                return

            # 将首次拆分的子查询的查询结果添加到集合中，默认使用倒排索引
            yield f'data: {json.dumps({"type": "process", "message": f"执行初始搜索，共 {len(new_queries)} 个查询", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)

            try:
                new_snippets = await self._search_and_filter_async(new_queries, [SearchToolEnum.INVERTED_INDEX for _ in range(len(new_queries))])
                yield f'data: {json.dumps({"type": "process", "message": f"初始搜索完成，找到 {len(new_snippets)} 个代码片段", "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)
            except Exception as e:
                trace_logger.error(f"初始搜索失败: {e}")
                yield f'data: {json.dumps({"type": "error", "message": f"初始搜索失败: {e}", "timestamp": time.time()})}\n\n'
                return

            result.all_queries.extend(new_queries)
            result.code_snippets.extend(new_snippets)

            # 针对每个query并行查询
            for iteration in range(self.config.max_iterations):
                if not new_queries:
                    trace_logger.info("未生成新查询，搜索结束")
                    yield f'data: {json.dumps({"type": "complete", "message": "未生成新查询，搜索结束", "timestamp": time.time()})}\n\n'
                    break

                yield f'data: {json.dumps({"type": "process", "message": f"开始第{iteration + 1}次迭代", "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)

                # 生成子查询
                try:
                    queries = await self._generate_new_queries_async(query, repo_struct, result.all_queries, result.code_snippets)
                except Exception as e:
                    trace_logger.error(f"生成新查询失败: {e}")
                    yield f'data: {json.dumps({"type": "error", "message": f"第{iteration + 1}次搜索: 生成新查询失败: {e}", "timestamp": time.time()})}\n\n'
                    break

                if not queries:
                    yield f'data: {json.dumps({"type": "complete", "message": f"第{iteration + 1}次搜索: 未生成新查询，搜索结束", "timestamp": time.time()})}\n\n'
                    break

                result.all_queries.extend([query[1] for query in queries])

                # 显示查询列表
                new_queries_str = '\n'.join([f"{i+1}. [{query[0].value}] {query[1]}" for i, query in enumerate(queries)])
                yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索查询列表:\n" + new_queries_str, "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)

                # 针对每个新查询并发执行查询
                yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索: 开始执行搜索...", "timestamp": time.time()})}\n\n'
                try:
                    new_snippets = await self._search_and_filter_async(queries=[query[1] for query in queries], search_tools=[query[0] for query in queries])
                    yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索: 搜索完成，找到 {len(new_snippets)} 个代码片段", "timestamp": time.time()})}\n\n'
                except Exception as e:
                    trace_logger.error(f"Iteration {iteration + 1}: Search Failed: {e}")
                    yield f'data: {json.dumps({"type": "error", "message": f"第{iteration + 1}次搜索: 搜索失败: {e}", "timestamp": time.time()})}\n\n'
                    break

                result.code_snippets.extend(new_snippets)
                if not new_snippets:
                    # DeepSearch的方法是预设首先拆分的搜索方向没有错误，如果子查询没有找到相关代码，说明当前搜索方向已经穷尽，可以结束搜索
                    trace_logger.warning(f"Iteration {iteration + 1}: Not Found Any Code Snippets")
                    yield f'data: {json.dumps({"type": "complete", "message": f"第{iteration + 1}次搜索: 未找到相关代码片段，搜索结束", "timestamp": time.time()})}\n\n'
                    break

                result.iterations = iteration + 1
                await asyncio.sleep(0.01)
            
            # 4. 文件级别合并和去重
            trace_logger.info("Merging Code Snippets at File Level...")
            yield f'data: {json.dumps({"type": "process", "message": "合并代码片段...", "timestamp": time.time()})}\n\n'
            result.code_snippets = self._merge_snippets_file(result.code_snippets)

            # 5. 重排序
            if result.code_snippets and new_queries:
                trace_logger.info("Reordering Code Snippets...")
                yield f'data: {json.dumps({"type": "process", "message": "重排序代码片段...", "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)
                result.code_snippets = await self._reorder_snippets_async(result.code_snippets, query, new_queries)

            # 6. 展示最终搜索结果
            total_snippets = len(result.code_snippets)
            total_files = len(set(snippet.file_path for snippet in result.code_snippets))
            
            trace_logger.info(f"搜索结果统计: 共找到 {total_snippets} 个代码片段，涉及 {total_files} 个文件")
            yield f'data: {json.dumps({"type": "process", "message": f"📊 搜索结果统计: 共找到 {total_snippets} 个代码片段，涉及 {total_files} 个文件", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)
            
            if total_snippets > 0:
                # 按文件分组展示结果
                file_groups = {}
                for snippet in result.code_snippets:
                    if snippet.file_path not in file_groups:
                        file_groups[snippet.file_path] = []
                    file_groups[snippet.file_path].append(snippet)
                
                yield f'data: {json.dumps({"type": "process", "message": "🔍 详细搜索结果:", "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)
                
                for file_path, snippets in file_groups.items():
                    yield f'data: {json.dumps({"type": "process", "message": f"📁 {file_path} ({len(snippets)} 个片段)", "timestamp": time.time()})}\n\n'
                    
                    for i, snippet in enumerate(snippets, 1):
                        snippet_info = f"  {i}. 行 {snippet.start_line}-{snippet.end_line}"
                        # 尝试从内容中提取函数名
                        lines = snippet.content.split('\n')
                        for line in lines[:3]:  # 只检查前3行
                            line = line.strip()
                            if line.startswith('def ') or line.startswith('class ') or line.startswith('function '):
                                func_match = line.split('(')[0].split(' ', 1)
                                if len(func_match) > 1:
                                    snippet_info += f" ({func_match[0]}: {func_match[1]})"
                                break
                        yield f'data: {json.dumps({"type": "process", "message": snippet_info, "timestamp": time.time()})}\n\n'
                    
                    await asyncio.sleep(0.01)
            else:
                yield f'data: {json.dumps({"type": "process", "message": "⚠️ 未找到相关代码片段", "timestamp": time.time()})}\n\n'
            
            trace_logger.info(f"DeepSearch Completed: {result.get_summary()}")
            yield f'data: {json.dumps({"type": "process", "message": f"✅ 搜索完成: {result.get_summary()}", "timestamp": time.time()})}\n\n'

            # 发送完成事件，包含搜索结果数据
            completion_data = {
                "type": "complete",
                "message": "搜索完成",
                "timestamp": time.time(),
                "result": {
                    "original_query": result.original_query,
                    "code_snippets": [snippet.model_dump() for snippet in result.code_snippets],
                    "total_snippets": len(result.code_snippets),
                    "total_files": len(set(snippet.file_path for snippet in result.code_snippets))
                }
            }
            yield f'data: {json.dumps(completion_data, ensure_ascii=False)}\n\n'
            await asyncio.sleep(0.01)  # 确保完成事件被及时发送

        except Exception as e:
            trace_logger.error(f"DeepSearch Failed: {e}")
            yield f'data: {json.dumps({"type": "error", "message": f"搜索失败: {e}", "timestamp": time.time()})}\n\n'
    


    async def _split_query_async(self, query: str, repo_struct: str, max_sub_queries: int = 3) -> List[str]:
        prompt = QUERY_SPLIT_PROMPT.format(
            original_query=query,
            max_subquries=max_sub_queries,
            repo_struct=repo_struct,
            SUB_QUERY_TAG=SearchToolEnum.SUB_QUERY.value
        )

        response_text = await self.llm_client.call_async(
            prompt,
            SYSTEM_PROMPTS['query_split'],
            stream=False
        )
        structure_results = self._extract_structure(response_text)
        sub_queries = [query.removeprefix(f"<{SearchToolEnum.SUB_QUERY}>").removesuffix(f"</{SearchToolEnum.SUB_QUERY}>") for _, query in structure_results]

        # 限制子查询数量
        return sub_queries[:max_sub_queries]


    async def _search_and_filter_async(self, queries: List[str], search_tools: List[SearchToolEnum] = []) -> List[CodeSnippet]:
        """
        异步搜索并过滤代码片段

        Args:
            queries: 查询列表
            search_tools: 搜索工具列表

        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        # 并行检索所有子查询
        trace_logger.info(f"Async Searching Tool: {search_tools}")
        trace_logger.info(f"Async Searching Sub-Queries...: {queries}")

        if not queries:
            return []

        # 使用asyncio.gather进行并发搜索
        search_tasks = []
        for query, search_tool in zip(queries, search_tools):
            # 使用异步搜索方法
            task = self.search_instance.search_async(query=query, search_tool=search_tool)
            search_tasks.append((query, task))

        # 等待所有搜索任务完成
        search_results = {}
        for query, task in search_tasks:
            try:
                snippets = await task
                trace_logger.info(f"Async Query '{query}' Found {len(snippets)} Code Snippets")
                search_results[query] = snippets
            except Exception as exc:
                trace_logger.info(f"Async Query '{query}' Search Failed: {exc}")
                search_results[query] = []

        # 统计总的代码片段数量
        all_snippets = [snippet for snippets in search_results.values() for snippet in snippets]
        trace_logger.info(f"Async Found {len(all_snippets)} Code Snippets, Start Filtering...")

        # 去重
        unique_snippets = self._merge_snippets(all_snippets)
        trace_logger.info(f"Async Filtered Snippets Deduplicated to {len(unique_snippets)} Unique Snippets")

        return unique_snippets



    async def _reorder_snippets_async(self, snippets: List[CodeSnippet], original_query: str, sub_queries: List[str]) -> List[CodeSnippet]:
        """
        异步版本的代码片段重排序
        """
        if not snippets:
            return []

        combined_query = original_query + "\n\t- " + "\n\t-".join(sub_queries)

        # 构建带索引的代码片段摘要
        code_summary = self._build_file_index_summary(snippets)

        prompt = SUBQUERY_REORDER_PROMPT.format(
            max_code_snippets=20,
            query=combined_query,
            code_snippets=code_summary
        )

        response_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['reorder'], stream=False)
        trace_logger.info(f"Async Reorder Response: {response_text}")

        # 解析LLM返回的JSON格式结果
        try:
            # 提取JSON内容
            import json

            # 尝试直接解析JSON
            try:
                score_dict = json.loads(response_text.strip().replace("\n", ""))
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试提取JSON块
                # 使用更复杂的正则表达式来匹配嵌套的JSON对象
                json_pattern = r'\{(?:[^{}]|{[^{}]*})*\}'
                json_match = re.search(json_pattern, response_text, re.DOTALL)
                if json_match:
                    try:
                        score_dict = json.loads(json_match.group())
                    except json.JSONDecodeError:
                        trace_logger.warning("提取的JSON格式无效")
                        return snippets  # 返回原始顺序
                else:
                    trace_logger.warning("无法从LLM响应中提取JSON格式的评分结果")
                    return snippets  # 返回原始顺序

            # 根据评分重新排序
            indexed_snippets = [(i, snippet) for i, snippet in enumerate(snippets)]

            # 创建评分映射 (将字符串键转换为整数)
            score_mapping = {}
            for key, score in score_dict.items():
                try:
                    index = int(key)
                    if 0 <= index < len(snippets):
                        score_mapping[index] = float(score)
                except (ValueError, TypeError):
                    trace_logger.warning(f"无效的索引或评分: {key}={score}")
                    continue

            # 按评分排序，没有评分的片段放在最后
            def get_score(item):
                index, _ = item
                return score_mapping.get(index, 0.0)

            sorted_indexed_snippets = sorted(indexed_snippets, key=get_score, reverse=True)

            # 提取排序后的代码片段，限制数量
            max_snippets = min(20, len(sorted_indexed_snippets))
            reordered_snippets = [snippet for _, snippet in sorted_indexed_snippets[:max_snippets]]

            return reordered_snippets

        except Exception as e:
            trace_logger.error(f"解析异步重排序结果失败: {e}")
            # 如果解析失败，返回原始片段（限制数量）
            return snippets[:20]

    def _merge_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        去重代码片段（基于文件路径和行号）
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            List[CodeSnippet]: 去重后的代码片段列表
        """
        seen_stack = []
        unique_snippets = []
        
        sorted_snippets = sorted(snippets, key=lambda x: (x.file_path, x.start_line, x.end_line))

        for snippet in sorted_snippets:
            # 使用文件路径和行号作为唯一标识
            key = (snippet.file_path, snippet.start_line, snippet.end_line)
            
            if seen_stack and seen_stack[-1][0] == key[0] and seen_stack[-1][1] <= key[1] <= seen_stack[-1][2]:
                # 更新seen_stack的end_line
                seen_stack[-1][2] = max(seen_stack[-1][2], key[2])
                
                # 如果end_line在前一个snippet后面，则合并snippet内容
                if unique_snippets[-1].end_line >= key[2]:
                    unique_snippets[-1].content += "\n".join(snippet.content.split('\n')[(unique_snippets[-1].end_line - snippet.start_line + 1):])
                    unique_snippets[-1].score += snippet.score # 合并后的score相加

                # 更新end_line
                unique_snippets[-1].end_line = seen_stack[-1][2]
                    
            else:   
                seen_stack.append(list(key)) # 因为元素可能被修改，tuple是不可变元组，因此使用list存入
                unique_snippets.append(snippet)

        return unique_snippets
    
    def _merge_snippets_file(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        按照文件路径合并snippets，如果文件路径相同，则合并为一个snippet
        如果相同文件的snippets之间的行号不连续，补充......指代中间的省略部分

        Args:
            snippets: 代码片段列表

        Returns:
            List[CodeSnippet]: 合并后的代码片段列表
        """
        if not snippets:
            return []

        unique_snippets = self._merge_snippets(snippets)

        file_groups = {}
        for snippet in unique_snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)

        merged_snippets = []

        for file_path, file_snippets in file_groups.items():
            # 按起始行号排序
            file_snippets.sort(key=lambda x: x.start_line)

            # 然后合并同一文件的所有片段，处理不连续的情况
            if len(file_snippets) == 1:
                merged_snippets.append(file_snippets[0])
            else:
                # 合并为一个大的片段，中间用省略号连接
                
                merged_content = "\n......\n".join(snippet.content for snippet in file_snippets)
                merged_startline = file_snippets[0].start_line
                merged_endline = file_snippets[-1].end_line
                
                merged_snippets.append(CodeSnippet(
                    file_path=file_path,
                    start_line=merged_startline,
                    end_line=merged_endline,
                    content=merged_content,
                    context_before="",
                    context_after="",
                    score=sum(snippet.score for snippet in file_snippets)
                ))

        return merged_snippets
    


    async def _generate_new_queries_async(
        self,
        original_query: str,
        repo_struct: str,
        previous_queries: List[str],
        code_snippets: List[CodeSnippet]
    ) -> Tuple[SearchToolEnum, List[str]]:
        """
        异步生成新的查询

        Args:
            original_query: 原始查询
            repo_struct: 仓库结构
            previous_queries: 之前的查询列表
            code_snippets: 已找到的代码片段

        Returns:
            Tuple[SearchToolEnum, List[str]]: 搜索工具类型和新查询列表
        """
        # 构建代码片段摘要

        prompt = GENERATE_NEW_QUERY_PROMPT.format(
            question=original_query,
            repo_struct=repo_struct,
            previous_queries=previous_queries,
            max_new_queries=self.config.max_new_queries,
            code_snippets=self._build_code_summary(code_snippets),
            tool_description=self.search_instance.description,
            tool_exampls=self.search_instance.examples
        )

        respose_text = await self.llm_client.call_async(prompt, SYSTEM_PROMPTS['generate_new_query'], stream=False)

        # 解析response_text，提取新的查询列表
        structure_results = self._extract_structure(respose_text)

        if not structure_results:
            return []

        return structure_results[:self.config.max_new_queries]

    def _build_code_summary(self, snippets: List[CodeSnippet]) -> str:
        """
        构建代码片段摘要
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            str: 代码摘要
        """
        if not snippets:
            return "暂无相关代码片段"
        
        # 按文件分组
        file_groups = {}
        for snippet in snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)
        
        # 构建摘要
        summary_parts = []
        file_index = 0
        for file_path, file_snippets in file_groups.items():
            summary_parts.append(f"{file_index}. 文件: {file_path}")
            for snippet in file_snippets[:3]:  # 每个文件最多显示3个片段
                summary_parts.append(f"  行 {snippet.start_line}-{snippet.end_line}: {snippet.content[:100]}...")
            file_index += 1

        return "\n".join(summary_parts)

    def _build_file_index_summary(self, snippets: List[CodeSnippet]) -> str:
        """
        构建文件索引摘要
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            str: 文件索引摘要
        """
        if not snippets:
            return "暂无相关代码片段"
        
        summary = ""
        for file_index, snippet in enumerate(snippets):
            summary += f"{file_index}. {snippet.file_path}: {snippet.start_line}-{snippet.end_line}\n"
            summary += f"  {snippet.content[:200]}\n"
            summary += f"--------------------------------------------\n\n"

        return summary
    
    def _extract_structure(self, response_text: str) -> List[Tuple[SearchToolEnum, str]]:
        """
        从LLM回复中提取元信息和结构化内容

        Args:
            response_text: 待提取的文本

        Returns:
            List[Tuple[SearchToolEnum, str]]: 包含搜索工具类型和查询内容的元组列表
        """
        # 提取<output>标签内的内容
        output_pattern = r'<output>(.*?)</output>'
        output_match = re.search(output_pattern, response_text, re.DOTALL)

        if not output_match:
            trace_logger.warning("未找到<output>标签")
            return []

        output_content = output_match.group(1).strip()

        if not output_content:
            trace_logger.info("输出内容为空，无需进一步搜索")
            return []

        results = []

        # 遍历所有搜索工具枚举，动态匹配标签
        for tool_enum in SearchToolEnum:
            # 跳过不需要匹配的工具类型
            if tool_enum in [SearchToolEnum.ANY, SearchToolEnum.EMBEDDING]:
                continue

            tool_tag = tool_enum.value
            pattern = f'<{tool_tag}>(.*?)</{tool_tag}>'
            matches = re.findall(pattern, output_content, re.DOTALL)

            if matches:
                # 为每个匹配创建一个元组
                for match in matches:
                    query_content = match.strip()
                    if query_content:
                        results.append((tool_enum, f"<{tool_tag}>{query_content}</{tool_tag}>")) # 还原XML格式，具体的参数交给对应的tool解析

        if results:
            trace_logger.info(f"提取到 {len(results)} 个查询")
            return results

        # 如果没有匹配到任何工具标签，尝试提取纯文本查询
        lines = [line.strip() for line in output_content.split('\n') if line.strip()]
        if lines:
            trace_logger.info(f"提取到纯文本查询: {lines}")
            # 将纯文本查询作为 term_sparse 查询返回
            return [(SearchToolEnum.TERM_SPRSE, line) for line in lines]

        trace_logger.warning("未能提取到有效查询")
        return []

        