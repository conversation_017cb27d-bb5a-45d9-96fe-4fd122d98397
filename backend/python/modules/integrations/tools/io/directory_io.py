import os
from utils.file import FileNode, build_file_tree


class DirectoryToolIO:
    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def read_dir(self, dir_path: str, max_files: int = 100) -> FileNode:
        """
        读取目录内容
        
        Args:
            dir_path: 目录路径
            
        Returns:
            str: 目录内容
        """
        return build_file_tree(root_dir=self.repo_path, start_dir=os.path.join(self.repo_path, dir_path), max_leaf_nodes=max_files)
        


