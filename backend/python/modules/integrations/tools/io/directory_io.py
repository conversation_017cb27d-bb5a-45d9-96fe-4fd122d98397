import os
from utils.file import FileNode, build_file_tree


class DirectoryToolIO:
    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def read(self, dir_path: str, max_files: int = 100) -> FileNode:
        """
        读取目录内容
        
        Args:
            dir_path: 目录路径
            
        Returns:
            str: 目录内容
        """
        return build_file_tree(root_dir=self.repo_path, start_dir=os.path.join(self.repo_path, dir_path), max_leaf_nodes=max_files)
        
    @property
    def description(self):
        return """- `directory_io`: Directory I/O operations tool for reading directory content.

  Parameters:
  - `path` (required): Relative directory path from repository root
  - `max_files` (optional): Maximum number of files to return (default: 100)

  Use cases:
  - Explore directory structure and contents
  - Identify files of interest for further analysis
"""

    @property
    def examples(self):
        return """
<output>
    <!-- Read directory content -->
    <directory_io>
    <path>src</path>
    <max_files>100</max_files>
    </directory_io>
</output>
"""

