from modules.common.constant import IOToolEnum

class AnyIOTool:
    def __init__(self, repo_path: str):
        # 使用对象的原因一方面是希望隔离不同请求之间的空间，另一方面是可以兼容带有状态的search过程
        # 当前将repo_path作为self属性是假设所有的读写操作发生在用户项目空间内，但是该假设未必成立，有可能要读取IDE的应用目录下的文件
        self.repo_path = repo_path
    
    def read(self, query: str, io_tool: IOToolEnum,  **kwargs) -> str:
        if io_tool == IOToolEnum.FILE:
            from modules.integrations.tools.io.file_io import FileIOTool
            return FileIOTool(self.repo_path).read(query, **kwargs)
        elif io_tool == IOToolEnum.DIRECTORY:
            from modules.integrations.tools.io.directory_io import DirectoryIOTool
            return DirectoryIOTool(self.repo_path).read(query, **kwargs)
        else:
            raise ValueError("未知的IO模式")
    
    def write(self, query: str, io_tool: IOToolEnum) -> None:
        if io_tool == IOToolEnum.FILE:
            from modules.integrations.tools.io.file_io import FileIOTool
            return FileIOTool(self.repo_path).write(query)
        else:
            raise ValueError("未知的IO模式")
    
    @property
    def description(self):
        pass