from modules.common.constant import IOMode

class AnyIOTool:
    def __init__(self, repo_path: str):
        # 使用对象的原因一方面是希望隔离不同请求之间的空间，另一方面是可以兼容带有状态的search过程
        # 当前将repo_path作为self属性是假设所有的读写操作发生在用户项目空间内，但是该假设未必成立，有可能要读取IDE的应用目录下的文件
        self.repo_path = repo_path
    
    def read(self, path: str, io_mode: IOMode, **kwargs) -> str:
        if io_mode == IOMode.READ:
            from modules.integrations.tools.io.file_io import FileIOTool
            return FileIOTool(self.repo_path).read_file(path, **kwargs)
        elif io_mode == IOMode.READ_DIR:
            from modules.integrations.tools.io.directory_io import DirectoryToolIO
            return DirectoryToolIO(self.repo_path).read_dir(path, **kwargs)
        else:
            raise ValueError("未知的IO模式")
    
    def write(self, path: str, content: str, io_mode: IOMode) -> None:
        if io_mode == IOMode.WRITE:
            from modules.integrations.tools.io.file_io import FileIOTool
            return FileIOTool(self.repo_path).rewrite_file(path, content)
        elif io_mode == IOMode.APPEND:
            from modules.integrations.tools.io.file_io import FileIOTool
            return FileIOTool(self.repo_path).append_file(path, content)
        else:
            raise ValueError("未知的IO模式")