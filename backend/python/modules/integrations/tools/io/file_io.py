import os
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element


class FileIOTool:
    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def read(self, query: str) -> str:
        """
        读取文件内容
        
        Args:
            query: 查询字符串
            
        Returns:
            str: 文件内容
        """
        pass

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'file_path': '',
            'start_line': 0,
            'end_line': 200
        }
        
        pass
    
    def _read_file(self, file_path: str, start_line: int=0, end_line: int=200) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            start_line: 起始行号
            end_line: 结束行号
            
        Returns:
            str: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'r', encoding='utf-8') as f:
            lines = f.readlines()
            return "\n".join(lines[max(0, start_line - 1):min(len(lines), end_line)])
    
    def _write_replace(self, file_path: str, content: str) -> None:
        """
        写入文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _write_append(self, file_path: str, content: str) -> None:
        """
        追加文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'a', encoding='utf-8') as f:
            f.write(content)
    
    @property
    def description(self):
        return """- `file_io`: File I/O operations tool for reading file content.

  Parameters:
  - `path` (required): Relative file path from repository root
  - `start_line` (optional): Starting line number for reading (default: 1, 1-based indexing)
  - `end_line` (optional): Ending line number for reading (default: 200)

  Use cases:
  - Read entire files or specific line ranges for code analysis
  - Inspect configuration files, source code, or documentation
"""
    
    @property
    def examples(self):
        return """
<output>
    <!-- Read specific line range -->
    <file_io>
    <path>src/main.py</path>
    <start_line>1</start_line>
    <end_line>10</end_line>
    </file_io>

    <!-- Read entire file (default behavior) -->
    <file_io>
    <path>src/main.py</path>
    </file_io>
</output>
"""