import os


class FileIOTool:
    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def read_file(self, file_path: str, start_line: int=0, end_line: int=200) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            start_line: 起始行号
            end_line: 结束行号
            
        Returns:
            str: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'r', encoding='utf-8') as f:
            lines = f.readlines()
            return "\n".join(lines[max(0, start_line - 1):min(len(lines), end_line)])
    
    def rewrite_file(self, file_path: str, content: str) -> None:
        """
        写入文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'w', encoding='utf-8') as f:
            f.write(content)
    
    def append_file(self, file_path: str, content: str) -> None:
        """
        追加文件内容
        
        Args:
            file_path: 文件路径
            content: 文件内容
        """
        with open(os.path.join(self.repo_path, file_path), 'a', encoding='utf-8') as f:
            f.write(content)
    
    @property
    def description(self):
        return "`- file_io`: Read the content of a specific file, supports reading a specific line range."
    
    @property
    def examples(self):
        return """
<output>
    <file_io>
    <path>src/main.py</path>
    <start_line>1</start_line>
    <end_line>10</end_line>
    </file_io>

    <file_io>
    <path>src/main.py</path>
    </file_io>
</output>
"""