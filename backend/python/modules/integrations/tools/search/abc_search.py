from typing import List
from enum import Enum
from abc import ABC, abstractmethod

from modules.common.schema import CodeSnippet

class SearchToolABC(ABC):
    """搜索引擎基类"""
    
    def __init__(self, repo_path: str, **args):
        pass
    
    @abstractmethod
    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        raise NotImplementedError("子类必须实现search方法")

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        异步搜索方法，默认实现调用同步方法
        子类可以重写此方法以提供真正的异步实现
        """
        import asyncio
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: self.search(query, **kwargs))

    @property
    @abstractmethod
    def description(self):
        raise NotImplementedError("子类必须实现description属性")

    @property
    @abstractmethod
    def examples(self):
        raise NotImplementedError("子类必须实现examples属性")
    
