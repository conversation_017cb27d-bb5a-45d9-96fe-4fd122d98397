from enum import Enum


class SuffixLanguage(Enum):
    JAVA = "java"
    PYTHON = "python"
    TEXT = "text"

class FileFilterMode(Enum):
    LOCAL = "local"
    EMBEDDING = "embedding"

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    INVERTED_INDEX = "inverted_index"
    TERM_SPRSE = "term_sparse"
    ANY = "any"
    SUB_QUERY = "sub_query"

class IOMode(Enum):
    READ = "read_file"
    READ_DIR = "read_dir"
    WRITE = "write_file"
    APPEND = "append_file"

