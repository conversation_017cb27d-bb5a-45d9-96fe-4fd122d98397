from enum import Enum


class SuffixLanguage(Enum):
    JAVA = "java"
    PYTHON = "python"
    TEXT = "text"

class FileFilterMode(Enum):
    LOCAL = "local"
    EMBEDDING = "embedding"

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    INVERTED_INDEX = "inverted_index"
    TERM_SPRSE = "term_sparse"
    ANY = "any"
    SUB_QUERY = "sub_query"

class IOToolEnum(Enum):
    FILE = "file_io"
    DIRECTORY = "directory_io"

