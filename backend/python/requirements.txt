# This file was autogenerated by uv via the following command:
#    uv pip compile ./pyproject.toml -o requirements.txt
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.15
    # via codebase-dev (./pyproject.toml)
aiosignal==1.4.0
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==3.7.1
    # via
    #   fastapi
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
attrs==25.3.0
    # via aiohttp
certifi==2025.8.3
    # via
    #   httpcore
    #   httpx
click==8.2.1
    # via
    #   codebase-dev (./pyproject.toml)
    #   uvicorn
deprecated==1.2.18
    # via codebase-dev (./pyproject.toml)
distro==1.9.0
    # via openai
fastapi==0.104.1
    # via codebase-dev (./pyproject.toml)
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   yarl
jiter==0.10.0
    # via openai
markdown-it-py==4.0.0
    # via rich
mdurl==0.1.2
    # via markdown-it-py
multidict==6.6.4
    # via
    #   aiohttp
    #   yarl
openai==1.99.9
    # via codebase-dev (./pyproject.toml)
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
pydantic==2.5.0
    # via
    #   codebase-dev (./pyproject.toml)
    #   fastapi
    #   openai
pydantic-core==2.14.1
    # via pydantic
pygments==2.19.2
    # via rich
python-dotenv==1.0.0
    # via
    #   codebase-dev (./pyproject.toml)
    #   uvicorn
python-multipart==0.0.6
    # via codebase-dev (./pyproject.toml)
pyyaml==6.0.2
    # via
    #   codebase-dev (./pyproject.toml)
    #   uvicorn
rich==14.1.0
    # via codebase-dev (./pyproject.toml)
sniffio==1.3.1
    # via
    #   anyio
    #   openai
starlette==0.27.0
    # via fastapi
tqdm==4.67.1
    # via openai
typing-extensions==4.14.1
    # via
    #   aiosignal
    #   fastapi
    #   openai
    #   pydantic
    #   pydantic-core
uvicorn==0.24.0
    # via codebase-dev (./pyproject.toml)
uvloop==0.21.0
    # via uvicorn
watchfiles==1.1.0
    # via uvicorn
websockets==15.0.1
    # via uvicorn
wrapt==1.17.3
    # via deprecated
yarl==1.20.1
    # via aiohttp
