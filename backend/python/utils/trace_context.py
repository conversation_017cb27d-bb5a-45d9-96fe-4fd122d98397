"""
Trace ID 上下文管理模块
用于在整个请求生命周期中传递 trace_id，支持多线程环境
"""

import contextvars
import uuid
from typing import Optional, Any, Dict
import threading
import functools
from concurrent.futures import ThreadPoolExecutor


# 创建 trace_id 上下文变量
trace_id_var: contextvars.ContextVar[str] = contextvars.ContextVar('trace_id', default='')

# 创建额外的上下文变量用于存储其他追踪信息
trace_context_var: contextvars.ContextVar[Dict[str, Any]] = contextvars.ContextVar('trace_context', default={})


def get_trace_id() -> str:
    """
    获取当前上下文的 trace_id
    
    Returns:
        str: 当前的 trace_id，如果没有设置则返回空字符串
    """
    return trace_id_var.get('')


def set_trace_id(trace_id: str) -> None:
    """
    设置当前上下文的 trace_id
    
    Args:
        trace_id: 要设置的 trace_id
    """
    trace_id_var.set(trace_id)


def generate_trace_id() -> str:
    """
    生成一个新的 trace_id
    
    Returns:
        str: 新生成的 trace_id
    """
    return str(uuid.uuid4())


def get_trace_context() -> Dict[str, Any]:
    """
    获取当前的追踪上下文
    
    Returns:
        Dict[str, Any]: 当前的追踪上下文
    """
    return trace_context_var.get({})


def set_trace_context(context: Dict[str, Any]) -> None:
    """
    设置追踪上下文
    
    Args:
        context: 要设置的上下文字典
    """
    trace_context_var.set(context)


def update_trace_context(**kwargs) -> None:
    """
    更新追踪上下文
    
    Args:
        **kwargs: 要更新的键值对
    """
    current_context = get_trace_context().copy()
    current_context.update(kwargs)
    set_trace_context(current_context)


class TraceContextManager:
    """
    Trace 上下文管理器
    用于在代码块中设置和清理 trace_id
    """
    
    def __init__(self, trace_id: Optional[str] = None, **context):
        """
        初始化上下文管理器
        
        Args:
            trace_id: 要设置的 trace_id，如果为 None 则生成新的
            **context: 额外的上下文信息
        """
        self.trace_id = trace_id or generate_trace_id()
        self.context = context
        self.token = None
        self.context_token = None
    
    def __enter__(self):
        """进入上下文"""
        self.token = trace_id_var.set(self.trace_id)
        if self.context:
            current_context = get_trace_context().copy()
            current_context.update(self.context)
            self.context_token = trace_context_var.set(current_context)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.token:
            trace_id_var.reset(self.token)
        if self.context_token:
            trace_context_var.reset(self.context_token)


def with_trace_id(trace_id: Optional[str] = None, **context):
    """
    装饰器：为函数设置 trace_id 上下文
    
    Args:
        trace_id: 要设置的 trace_id，如果为 None 则生成新的
        **context: 额外的上下文信息
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with TraceContextManager(trace_id, **context):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def copy_context_to_thread(func):
    """
    装饰器：将当前上下文复制到新线程中
    
    Args:
        func: 要在新线程中执行的函数
    
    Returns:
        装饰器函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 获取当前上下文
        current_trace_id = get_trace_id()
        current_context = get_trace_context()
        
        def context_aware_func(*args, **kwargs):
            # 在新线程中设置上下文
            with TraceContextManager(current_trace_id, **current_context):
                return func(*args, **kwargs)
        
        return context_aware_func(*args, **kwargs)
    return wrapper


class TraceAwareThreadPoolExecutor(ThreadPoolExecutor):
    """
    支持 trace 上下文传递的线程池执行器
    """
    
    def submit(self, fn, *args, **kwargs):
        """
        提交任务到线程池，自动传递当前的 trace 上下文
        
        Args:
            fn: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        
        Returns:
            Future: 任务的 Future 对象
        """
        # 获取当前上下文
        current_trace_id = get_trace_id()
        current_context = get_trace_context()
        
        def context_aware_fn(*args, **kwargs):
            # 在新线程中设置上下文
            with TraceContextManager(current_trace_id, **current_context):
                return fn(*args, **kwargs)
        
        return super().submit(context_aware_fn, *args, **kwargs)


def get_current_thread_info() -> Dict[str, Any]:
    """
    获取当前线程信息
    
    Returns:
        Dict[str, Any]: 包含线程信息的字典
    """
    current_thread = threading.current_thread()
    return {
        'thread_id': current_thread.ident,
        'thread_name': current_thread.name,
        'is_main_thread': current_thread is threading.main_thread()
    }


def format_trace_info() -> str:
    """
    格式化当前的追踪信息
    
    Returns:
        str: 格式化后的追踪信息字符串
    """
    trace_id = get_trace_id()
    context = get_trace_context()
    thread_info = get_current_thread_info()
    
    parts = []
    if trace_id:
        parts.append(f"trace_id={trace_id}")
    
    if context:
        for key, value in context.items():
            parts.append(f"{key}={value}")
    
    parts.append(f"thread={thread_info['thread_name']}")
    
    return " | ".join(parts) if parts else ""


# 便捷函数
def start_trace(trace_id: Optional[str] = None, **context) -> str:
    """
    开始一个新的追踪
    
    Args:
        trace_id: 指定的 trace_id，如果为 None 则生成新的
        **context: 额外的上下文信息
    
    Returns:
        str: 设置的 trace_id
    """
    actual_trace_id = trace_id or generate_trace_id()
    set_trace_id(actual_trace_id)
    if context:
        update_trace_context(**context)
    return actual_trace_id


def clear_trace() -> None:
    """
    清除当前的追踪上下文
    """
    set_trace_id('')
    set_trace_context({})
