# 日志系统文档

## 概述

这个日志系统基于Python标准库的logging模块，集成了Rich库用于美化控制台输出，并支持配置文件驱动的日志管理。系统提供了文件轮转、多级别日志记录、结构化日志等功能。

**🆕 现在支持 trace_id 追踪功能！** 所有日志都会自动包含 trace_id 信息，方便分布式系统的问题排查。

## 特性

- ✅ **配置驱动**: 通过配置文件控制日志行为
- ✅ **文件轮转**: 自动切分日志文件，防止文件过大
- ✅ **多级别日志**: 支持DEBUG、INFO、WARNING、ERROR、CRITICAL级别
- ✅ **美化输出**: 使用Rich库美化控制台日志显示
- ✅ **便捷函数**: 提供简单易用的日志记录函数
- ✅ **命名Logger**: 支持为不同模块创建独立的logger
- ✅ **异常追踪**: 自动记录异常堆栈信息
- ✅ **性能友好**: 合理的缓存和处理器配置
- 🆕 **Trace ID 支持**: 自动追踪请求链路，支持多线程环境
- 🆕 **上下文传递**: 自动在子线程中传递 trace_id
- 🆕 **向后兼容**: 完全兼容原有的 logger 使用方式

## 配置

日志系统通过 `core.config` 模块获取配置，支持以下配置项：

```yaml
log:
  dir: ./Logs              # 日志目录
  name: app.log            # 日志文件名
  level: info              # 日志级别 (debug/info/warning/error/critical)
  max_size_m: 5            # 日志文件最大大小(MB)
```

## 基本使用

### 1. 导入日志模块

```python
from utils.logger import logger, info, warning, error, critical, debug, get_logger
from utils.trace_context import TraceContextManager  # 用于 trace_id 功能
```

### 2. 使用默认logger（自动支持 trace_id）

```python
from utils.logger import logger

# 普通使用（向后兼容）
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
logger.critical("这是一条严重错误日志")
logger.debug("这是一条调试日志")

# 带 trace_id 的使用
from utils.trace_context import TraceContextManager

with TraceContextManager("my-trace-001", user_id=12345):
    logger.info("这条日志会包含 trace_id")
    # 输出: 2025-08-20 17:45:00 - module - INFO - [trace_id=my-trace-001 | user_id=12345] - 这条日志会包含 trace_id
```

### 3. 使用便捷函数

```python
from utils.logger import info, warning, error, critical, debug

info("使用便捷函数记录信息")
warning("使用便捷函数记录警告")
error("使用便捷函数记录错误")
critical("使用便捷函数记录严重错误")
debug("使用便捷函数记录调试信息")
```

### 4. 获取命名logger

```python
from utils.logger import get_logger

# 为不同模块创建独立的logger
api_logger = get_logger("api")
db_logger = get_logger("database")
auth_logger = get_logger("auth")

api_logger.info("API请求处理开始")
db_logger.info("数据库连接建立")
auth_logger.warning("用户认证失败")
```

## 高级用法

### 1. 格式化日志

```python
from utils.logger import logger

user_id = 12345
action = "登录"
ip_address = "*************"

# 使用f-string格式化
logger.info(f"用户 {user_id} 从 {ip_address} 执行 {action} 操作")

# 使用%格式化
logger.warning("用户 %s 尝试访问受限资源", user_id)

# 使用.format()格式化
logger.error("操作失败: 用户={}, 动作={}, IP={}".format(user_id, action, ip_address))
```

### 2. 异常日志记录

```python
from utils.logger import logger

try:
    result = 10 / 0
except ZeroDivisionError as e:
    # 记录异常和堆栈信息
    logger.exception("除零错误发生")
    
    # 或者只记录错误信息
    logger.error("计算错误: %s", str(e))
```

### 3. 在类中使用logger

```python
from utils.logger import get_logger

class UserService:
    def __init__(self):
        # 为类创建专用logger
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def create_user(self, username, email):
        self.logger.info(f"开始创建用户: {username}")
        
        try:
            # 业务逻辑
            if not username:
                raise ValueError("用户名不能为空")
            
            self.logger.info(f"用户创建成功: {username}")
            return {"id": 123, "username": username}
            
        except ValueError as e:
            self.logger.error(f"用户创建失败: {e}")
            raise
        except Exception as e:
            self.logger.exception("用户创建过程中发生未知错误")
            raise
```

### 4. 性能日志记录

```python
import time
from utils.logger import get_logger

perf_logger = get_logger("performance")

def timed_operation(operation_name):
    start_time = time.time()
    perf_logger.info(f"开始执行: {operation_name}")
    
    # 执行操作
    time.sleep(1)
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    if elapsed > 1.0:
        perf_logger.warning(f"操作耗时过长: {operation_name} 耗时 {elapsed:.2f}s")
    else:
        perf_logger.info(f"操作完成: {operation_name} 耗时 {elapsed:.2f}s")
```

### 5. 结构化日志

```python
from utils.logger import logger

# 使用extra参数添加结构化数据
logger.info("用户操作", extra={
    'user_id': 12345,
    'action': 'login',
    'ip': '*************',
    'timestamp': '2025-08-13T12:00:00Z'
})
```

## 日志级别

| 级别 | 数值 | 用途 |
|------|------|------|
| DEBUG | 10 | 详细的调试信息，通常只在诊断问题时使用 |
| INFO | 20 | 一般信息，确认程序按预期工作 |
| WARNING | 30 | 警告信息，程序仍能正常工作，但可能有问题 |
| ERROR | 40 | 错误信息，程序某些功能无法正常工作 |
| CRITICAL | 50 | 严重错误，程序可能无法继续运行 |

## 文件轮转

日志系统使用 `RotatingFileHandler` 实现文件轮转：

- **最大文件大小**: 由配置文件中的 `max_size_m` 控制
- **备份文件数量**: 默认保留5个备份文件
- **文件命名**: `app.log`, `app.log.1`, `app.log.2`, ...

当日志文件达到最大大小时，系统会自动：
1. 将当前文件重命名为 `app.log.1`
2. 创建新的 `app.log` 文件
3. 删除最旧的备份文件（如果超过备份数量限制）

## 配置回退机制

如果配置文件加载失败，日志系统会使用默认配置：

```python
default_config = {
    "dir": "./logs",
    "name": "app.log",
    "level": "info",
    "max_size_m": 5
}
```

## API 参考

### 函数

#### `get_logger(name: str = None) -> logging.Logger`

获取命名logger实例。

**参数**:
- `name`: logger名称，如果为None则自动使用调用模块的名称

**返回值**: 配置好的Logger实例

#### 便捷函数

- `debug(msg, *args, **kwargs)`: 记录DEBUG级别日志
- `info(msg, *args, **kwargs)`: 记录INFO级别日志
- `warning(msg, *args, **kwargs)`: 记录WARNING级别日志
- `error(msg, *args, **kwargs)`: 记录ERROR级别日志
- `critical(msg, *args, **kwargs)`: 记录CRITICAL级别日志

### 变量

- `logger`: 默认logger实例
- `log_config`: 当前日志配置对象
- `log_level`: 当前日志级别
- `file_handler`: 文件处理器实例
- `console_handler`: 控制台处理器实例

## 最佳实践

1. **使用合适的日志级别**: 
   - DEBUG: 调试信息
   - INFO: 正常操作信息
   - WARNING: 潜在问题
   - ERROR: 错误但程序可继续
   - CRITICAL: 严重错误

2. **为不同模块使用不同的logger**:
   ```python
   api_logger = get_logger("api")
   db_logger = get_logger("database")
   ```

3. **记录异常时使用exception()方法**:
   ```python
   try:
       # 代码
   except Exception:
       logger.exception("操作失败")
   ```

4. **避免在循环中记录过多日志**:
   ```python
   # 不好的做法
   for i in range(10000):
       logger.info(f"处理第{i}项")
   
   # 好的做法
   logger.info("开始处理10000项数据")
   for i in range(10000):
       if i % 1000 == 0:
           logger.info(f"已处理{i}项")
   logger.info("数据处理完成")
   ```

5. **使用结构化日志便于分析**:
   ```python
   logger.info("用户登录", extra={
       'user_id': user_id,
       'ip': request.remote_addr,
       'user_agent': request.user_agent
   })
   ```

## 故障排除

### 常见问题

1. **日志文件不存在**
   - 检查日志目录权限
   - 确认配置文件路径正确

2. **日志级别不生效**
   - 检查配置文件中的level设置
   - 确认配置文件格式正确

3. **控制台没有日志输出**
   - 检查logger的propagate设置
   - 确认控制台处理器已添加

### 调试技巧

```python
# 检查当前日志配置
from utils.logger import log_config, logger

print(f"日志级别: {log_config.level}")
print(f"日志文件: {log_config.dir}/{log_config.name}")
print(f"Logger级别: {logger.level}")
print(f"处理器数量: {len(logger.handlers)}")
```

## 示例代码

完整的使用示例请参考 `examples/logger_usage.py` 文件。
