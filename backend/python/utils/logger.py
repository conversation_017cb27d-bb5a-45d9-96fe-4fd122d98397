import logging
import os
from pathlib import Path
from logging.handlers import RotatingFileHandler
from rich.logging import RichHandler
from logging import Formatter
from datetime import datetime

from core.config import get_config

log_config = get_config().log

# 项目默认日志管理器
logger = logging.getLogger(__name__)

# 根据配置文件设置日志级别
log_level_map = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}
log_level = log_level_map.get(log_config.level.lower(), logging.INFO)
logger.setLevel(log_level)

# 创建自定义格式器
formatter = logging.Formatter(
    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 确保日志目录存在
log_dir = Path(log_config.dir)
log_dir.mkdir(parents=True, exist_ok=True)

# 生成带时间戳的日志文件名
startup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file_name = log_config.name
# 在文件扩展名前插入时间戳
if '.' in log_file_name:
    name_parts = log_file_name.rsplit('.', 1)
    timestamped_name = f"{name_parts[0]}_{startup_time}.{name_parts[1]}"
else:
    timestamped_name = f"{log_file_name}_{startup_time}"

# 创建文件处理器 - 使用RotatingFileHandler实现自动切分
log_file_path = log_dir / timestamped_name
max_bytes = log_config.max_size_m * 1024 * 1024  # 转换为字节
backup_count = 5  # 保留5个备份文件

file_handler = RotatingFileHandler(
    filename=str(log_file_path),
    maxBytes=max_bytes,
    backupCount=backup_count,
    encoding="utf-8"
)
file_handler.setFormatter(formatter)
file_handler.setLevel(log_level)

logger.addHandler(file_handler)

# 创建控制台处理器
console_handler = RichHandler(markup=True, rich_tracebacks=True)
console_handler.setFormatter(logging.Formatter('%(message)s'))
console_handler.setLevel(log_level)

# 添加控制台处理器到logger
logger.addHandler(console_handler)

# 防止重复日志（不传播到根logger）
logger.propagate = False

# 为了兼容性，也设置根logger的基本配置（但级别设为WARNING以减少第三方库日志）
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[file_handler]
)

# 原始的 get_logger 函数（内部使用）
def _get_original_logger(name: str = None) -> logging.Logger:
    """
    获取原始的日志记录器（内部使用）

    Args:
        name: 日志记录器名称，默认使用调用模块的名称

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    if name is None:
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')

    logger_instance = logging.getLogger(name)
    logger_instance.setLevel(log_level)

    # 如果logger还没有处理器，添加我们的处理器
    if not logger_instance.handlers:
        logger_instance.addHandler(file_handler)
        logger_instance.addHandler(console_handler)
        logger_instance.propagate = False

    return logger_instance

# 设置支持 trace_id 的日志格式
def setup_trace_logging():
    """设置支持 trace_id 的日志格式"""
    try:
        from utils.trace_logger import TraceFormatter

        # 创建支持 trace 的格式器
        trace_formatter = TraceFormatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(trace_info)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 更新文件处理器的格式器
        file_handler.setFormatter(trace_formatter)

        logger.info("Trace logging 设置完成")
    except ImportError:
        # 如果 trace_logger 模块不可用，保持原有格式
        logger.warning("Trace logging 模块不可用，使用标准日志格式")

# 初始化 trace logging
setup_trace_logging()

# 记录日志系统初始化信息
logger.info(f"日志系统初始化完成 - 级别: {log_config.level}, 文件: {log_file_path}, 最大大小: {log_config.max_size_m}MB, 启动时间: {startup_time}")

# 创建支持 trace 的默认 logger 实例和便捷函数
try:
    from utils.trace_logger import get_trace_logger
    # 创建支持 trace 的默认 logger
    trace_aware_logger = get_trace_logger(__name__)

    # 为了向后兼容，将 trace-aware logger 的方法导出为便捷函数
    def debug(msg, *args, **kwargs):
        """记录DEBUG级别日志（支持trace）"""
        trace_aware_logger.debug(msg, *args, **kwargs)

    def info(msg, *args, **kwargs):
        """记录INFO级别日志（支持trace）"""
        trace_aware_logger.info(msg, *args, **kwargs)

    def warning(msg, *args, **kwargs):
        """记录WARNING级别日志（支持trace）"""
        trace_aware_logger.warning(msg, *args, **kwargs)

    def error(msg, *args, **kwargs):
        """记录ERROR级别日志（支持trace）"""
        trace_aware_logger.error(msg, *args, **kwargs)

    def critical(msg, *args, **kwargs):
        """记录CRITICAL级别日志（支持trace）"""
        trace_aware_logger.critical(msg, *args, **kwargs)

    # 更新 get_logger 函数以返回 trace-aware logger
    def get_logger(name: str = None):
        """
        获取支持 trace_id 的日志记录器

        Args:
            name: 日志记录器名称，默认使用调用模块的名称

        Returns:
            TraceAwareLogger: 支持 trace_id 的日志记录器
        """
        return get_trace_logger(name)

    logger.info("Trace-aware logger 初始化完成")

except ImportError:
    # 如果 trace_logger 不可用，使用原始的便捷函数
    def debug(msg, *args, **kwargs):
        """记录DEBUG级别日志"""
        logger.debug(msg, *args, **kwargs)

    def info(msg, *args, **kwargs):
        """记录INFO级别日志"""
        logger.info(msg, *args, **kwargs)

    def warning(msg, *args, **kwargs):
        """记录WARNING级别日志"""
        logger.warning(msg, *args, **kwargs)

    def error(msg, *args, **kwargs):
        """记录ERROR级别日志"""
        logger.error(msg, *args, **kwargs)

    def critical(msg, *args, **kwargs):
        """记录CRITICAL级别日志"""
        logger.critical(msg, *args, **kwargs)

    logger.warning("Trace logger 不可用，使用标准 logger")