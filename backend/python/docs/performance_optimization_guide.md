# 并发性能优化指南

## 已实现的优化

### 1. 异步化改造
- ✅ LLM客户端异步化 (`call_async`方法)
- ✅ 搜索工具异步化 (grep搜索支持`search_async`)
- ✅ DeepSearch核心逻辑异步化
- ✅ API接口真正异步处理

### 2. 并发控制
- ✅ 增加线程池大小配置 (`max_workers: 16`)
- ✅ LLM请求并发限制 (`max_concurrent_requests: 10`)
- ✅ 优化uvicorn启动参数

### 3. 性能提升结果
- **并发加速比**: 3.98倍
- **吞吐量**: 从0.03提升到0.13请求/秒
- **响应时间**: 并发请求平均快27%

## 进一步优化建议

### 1. 缓存策略优化

#### LLM响应缓存
```python
# 在modules/llm/llm_client.py中添加
import hashlib
from functools import lru_cache

class LLMClient:
    def __init__(self):
        self.response_cache = {}  # 或使用Redis
    
    def _get_cache_key(self, prompt: str, system_prompt: str) -> str:
        content = f"{system_prompt}|{prompt}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def call_async_with_cache(self, prompt: str, system_prompt: str = "...") -> str:
        cache_key = self._get_cache_key(prompt, system_prompt)
        if cache_key in self.response_cache:
            return self.response_cache[cache_key]
        
        result = await self.call_async(prompt, system_prompt)
        self.response_cache[cache_key] = result
        return result
```

#### 搜索结果缓存
```python
# 在modules/deepsearch/deep_search.py中添加
import pickle
from pathlib import Path

class DeepSearch:
    def __init__(self, ...):
        self.cache_dir = Path("./data/cache/search_results")
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_search_cache_key(self, query: str) -> str:
        return hashlib.md5(f"{self.repo_path}|{query}".encode()).hexdigest()
    
    async def search_async_with_cache(self, query: str) -> SearchResult:
        cache_key = self._get_search_cache_key(query)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        if cache_file.exists():
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        
        result = await self.search_async(query)
        
        with open(cache_file, 'wb') as f:
            pickle.dump(result, f)
        
        return result
```

### 2. 数据库连接池优化

如果使用数据库，建议配置连接池：

```yaml
# config.yaml
database:
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
```

### 3. 内存优化

#### 大对象处理
```python
# 对于大型搜索结果，使用生成器
async def search_results_generator(self, query: str):
    async for batch in self.search_in_batches(query):
        yield batch

# 限制内存使用
import psutil
import gc

def check_memory_usage():
    process = psutil.Process()
    memory_percent = process.memory_percent()
    if memory_percent > 80:  # 超过80%内存使用
        gc.collect()  # 强制垃圾回收
```

### 4. 监控和指标

#### 性能监控
```python
# utils/performance_monitor.py
import time
import asyncio
from functools import wraps

def monitor_performance(func_name: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{func_name} 执行时间: {duration:.2f}秒")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func_name} 执行失败，耗时: {duration:.2f}秒, 错误: {e}")
                raise
        return wrapper
    return decorator

# 使用示例
@monitor_performance("深度搜索")
async def search_async(self, query: str) -> SearchResult:
    # 原有逻辑
```

#### 请求限流
```python
# utils/rate_limiter.py
import asyncio
from collections import defaultdict
import time

class RateLimiter:
    def __init__(self, max_requests: int = 100, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = defaultdict(list)
    
    async def is_allowed(self, client_id: str) -> bool:
        now = time.time()
        # 清理过期请求
        self.requests[client_id] = [
            req_time for req_time in self.requests[client_id]
            if now - req_time < self.time_window
        ]
        
        if len(self.requests[client_id]) >= self.max_requests:
            return False
        
        self.requests[client_id].append(now)
        return True
```

### 5. 配置调优

#### 推荐的生产环境配置
```yaml
# config.yaml
api:
  host: "0.0.0.0"
  port: 3451
  reload: false  # 生产环境关闭热重载
  workers: 4     # 多进程部署

deepsearch:
  max_iterations: 2
  max_sub_queries: 5
  max_new_queries: 3
  max_workers: 32  # 根据CPU核心数调整

llm:
  timeout: 120
  max_concurrent_requests: 20
  
# 新增性能配置
performance:
  enable_caching: true
  cache_ttl: 3600  # 缓存1小时
  max_memory_usage: 80  # 最大内存使用百分比
  enable_monitoring: true
```

### 6. 部署优化

#### Docker配置
```dockerfile
# 使用多阶段构建
FROM python:3.12-slim as builder
# ... 构建阶段

FROM python:3.12-slim
# 设置资源限制
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 优化Python性能
ENV PYTHONHASHSEED=random
ENV PYTHONOPTIMIZE=1

COPY --from=builder /app /app
WORKDIR /app

# 使用gunicorn部署
CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:3451"]
```

#### Nginx配置
```nginx
upstream backend {
    server 127.0.0.1:3451;
    server 127.0.0.1:3452;
    server 127.0.0.1:3453;
    server 127.0.0.1:3454;
}

server {
    listen 80;
    
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_buffering off;  # 对于流式响应
        proxy_read_timeout 300s;
        proxy_connect_timeout 10s;
    }
}
```

## 性能测试

### 压力测试脚本
```bash
# 使用wrk进行压力测试
wrk -t12 -c400 -d30s --script=post.lua http://localhost:3451/api/v1/search

# post.lua内容
wrk.method = "POST"
wrk.body = '{"query":"search function","workspace_name":"test","search_tool":"any","is_stream":false}'
wrk.headers["Content-Type"] = "application/json"
```

### 监控指标
- 响应时间 (P50, P95, P99)
- 吞吐量 (RPS)
- 错误率
- 内存使用率
- CPU使用率
- 并发连接数

## 总结

通过以上优化，你的Python后端应该能够：
1. 支持更高的并发请求
2. 更好地利用系统资源
3. 提供更快的响应时间
4. 具备更好的可扩展性

建议按优先级逐步实施这些优化，并在每个阶段进行性能测试验证效果。
