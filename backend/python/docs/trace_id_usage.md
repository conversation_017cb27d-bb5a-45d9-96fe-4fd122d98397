# Trace ID 日志追踪系统使用指南

## 概述

本系统实现了基于 `trace_id` 的分布式日志追踪功能，支持在多线程环境下自动传递追踪标识，方便问题排查和性能分析。

## 核心特性

- ✅ **自动上下文传递**: 使用 `contextvars` 实现线程安全的上下文传递
- ✅ **多线程支持**: 子线程自动继承父线程的 trace_id
- ✅ **日志格式增强**: 日志中自动包含 trace_id 和上下文信息
- ✅ **装饰器支持**: 提供便捷的装饰器用于函数追踪
- ✅ **API 集成**: 在 API 入口自动设置 trace_id
- ✅ **性能监控**: 支持执行时间记录和性能分析

## 快速开始

### 1. 基本使用

```python
from utils.trace_context import TraceContextManager
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

# 使用 trace 上下文
with TraceContextManager("my-trace-001", user_id=12345):
    logger.info("这条日志会包含 trace_id")
    # 输出: 2025-08-20 10:30:00 - __main__ - INFO - [trace_id=my-trace-001 | user_id=12345] - 这条日志会包含 trace_id
```

### 2. API 请求追踪

在 API 入口设置 trace_id：

```python
from utils.trace_context import TraceContextManager
from utils.trace_logger import get_trace_logger

@app.post("/api/search")
async def search_api(request: SearchRequest):
    # 使用请求中的 trace_id
    with TraceContextManager(
        trace_id=request.trace_id,
        endpoint="/api/search",
        user_id=request.user_id
    ):
        logger = get_trace_logger(__name__)
        logger.info("开始处理搜索请求")
        
        # 所有后续的日志都会包含 trace_id
        result = process_search(request.query)
        
        logger.info("搜索请求处理完成")
        return result
```

### 3. 多线程环境

```python
from utils.trace_context import TraceAwareThreadPoolExecutor
from utils.trace_logger import get_trace_logger

def worker_task(task_id):
    logger = get_trace_logger("worker")
    logger.info(f"执行任务 {task_id}")  # 自动包含父线程的 trace_id
    return f"result_{task_id}"

# 在主线程中设置 trace_id
with TraceContextManager("parallel-work-001"):
    logger = get_trace_logger("main")
    logger.info("开始并行处理")
    
    # 使用支持 trace 传递的线程池
    with TraceAwareThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(worker_task, i) for i in range(10)]
        results = [f.result() for f in futures]
    
    logger.info("并行处理完成")
```

## 高级功能

### 1. 装饰器

#### 函数进入/退出追踪

```python
from utils.trace_logger import log_function_entry_exit

@log_function_entry_exit()
def important_function(param1, param2):
    # 函数逻辑
    return result
```

#### 执行时间监控

```python
from utils.trace_logger import log_execution_time

@log_execution_time()
def slow_function():
    # 耗时操作
    time.sleep(2)
    return "done"
```

#### 组合使用

```python
@log_function_entry_exit("自定义函数名")
@log_execution_time()
def complex_function():
    # 复杂业务逻辑
    pass
```

### 2. 手动 trace 管理

```python
from utils.trace_context import start_trace, clear_trace, get_trace_id

# 手动开始追踪
trace_id = start_trace("manual-trace", operation="data_processing")
logger.info("手动设置的追踪")

# 获取当前 trace_id
current_id = get_trace_id()
print(f"当前 trace_id: {current_id}")

# 清除追踪
clear_trace()
```

### 3. 便捷函数

```python
from utils.trace_logger import trace_info, trace_warning, trace_error

# 直接使用便捷函数记录日志
trace_info("信息日志")
trace_warning("警告日志")
trace_error("错误日志")
```

## 配置说明

### 日志格式

系统会自动配置支持 trace_id 的日志格式：

```
2025-08-20 10:30:00 - module_name - INFO - [trace_id=abc-123 | user_id=456] - 日志消息
```

### 上下文信息

除了 `trace_id`，还可以添加其他上下文信息：

```python
with TraceContextManager(
    "trace-001",
    user_id=12345,
    operation="search",
    ip_address="*************",
    request_id="req-456"
):
    logger.info("包含丰富上下文的日志")
```

## 最佳实践

### 1. API 层面

- 在每个 API 入口设置 trace_id
- 使用有意义的 trace_id 格式（如：`api-search-{timestamp}-{random}`）
- 在响应头中返回 trace_id 便于客户端追踪

```python
@app.post("/api/endpoint")
async def api_endpoint(request: Request):
    trace_id = request.trace_id or generate_trace_id()
    
    with TraceContextManager(trace_id, endpoint=request.url.path):
        # 处理逻辑
        result = process_request(request)
        
        # 在响应头中返回 trace_id
        return JSONResponse(
            content=result,
            headers={"X-Trace-ID": trace_id}
        )
```

### 2. 服务层面

- 在关键业务逻辑入口记录日志
- 使用合适的日志级别
- 记录重要的业务参数

```python
def search_service(query: str, filters: dict):
    logger = get_trace_logger(__name__)
    
    logger.info(f"开始搜索: query='{query}', filters={filters}")
    
    try:
        results = perform_search(query, filters)
        logger.info(f"搜索完成: 找到 {len(results)} 个结果")
        return results
    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        raise
```

### 3. 错误处理

- 在异常处理中保持 trace_id
- 记录详细的错误信息

```python
try:
    risky_operation()
except Exception as e:
    logger.exception("操作失败")  # 自动包含 trace_id 和堆栈信息
    raise
```

## 性能考虑

### 1. 上下文开销

- `contextvars` 的性能开销很小
- 避免在上下文中存储大量数据
- 只存储必要的追踪信息

### 2. 日志性能

- 合理设置日志级别
- 避免在高频调用的函数中记录过多日志
- 使用异步日志处理（如果需要）

### 3. 线程池配置

- 根据实际需求调整线程池大小
- 避免创建过多的线程池实例

## 故障排查

### 1. trace_id 丢失

**问题**: 在某些地方看不到 trace_id

**解决方案**:
- 检查是否正确设置了 TraceContextManager
- 确认使用的是 TraceAwareThreadPoolExecutor
- 验证日志格式器配置

### 2. 性能问题

**问题**: 系统性能下降

**解决方案**:
- 检查日志级别设置
- 减少不必要的上下文信息
- 优化日志输出频率

### 3. 多线程问题

**问题**: 子线程中没有 trace_id

**解决方案**:
- 使用 TraceAwareThreadPoolExecutor 替代标准 ThreadPoolExecutor
- 检查线程创建方式
- 确认上下文传递逻辑

## 示例代码

完整的使用示例请参考：
- `test_trace_id.py` - 功能测试示例
- `server/api/v1/api.py` - API 集成示例
- `modules/deepsearch/deep_search.py` - 业务逻辑集成示例

## 扩展功能

### 1. 自定义格式器

```python
from utils.trace_logger import TraceFormatter

# 创建自定义格式器
custom_formatter = TraceFormatter(
    fmt='%(asctime)s [%(trace_info)s] %(levelname)s: %(message)s',
    trace_format='TRACE:{trace_info}'
)
```

### 2. 集成监控系统

```python
def send_to_monitoring(trace_id, metrics):
    # 发送到监控系统
    monitoring_client.send({
        'trace_id': trace_id,
        'metrics': metrics,
        'timestamp': time.time()
    })

# 在关键点记录指标
with TraceContextManager("operation-001"):
    start_time = time.time()
    
    # 执行操作
    result = perform_operation()
    
    # 记录指标
    execution_time = time.time() - start_time
    send_to_monitoring(get_trace_id(), {
        'execution_time': execution_time,
        'result_count': len(result)
    })
```

## 总结

通过使用这套 trace_id 系统，您可以：

1. **快速定位问题**: 通过 trace_id 快速找到相关的所有日志
2. **分析性能瓶颈**: 追踪请求在各个组件中的执行时间
3. **监控系统健康**: 统计各种操作的成功率和响应时间
4. **优化用户体验**: 快速响应用户问题和反馈

系统设计考虑了性能、易用性和扩展性，可以满足大多数分布式系统的日志追踪需求。
