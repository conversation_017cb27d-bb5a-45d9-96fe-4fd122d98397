import React from 'react';
import { cn } from '@/utils/cn';
import type { InputType } from '@/types';

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  type?: InputType;
  error?: string;
  label?: string;
  icon?: React.ReactNode;
}

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  type: 'textarea';
  error?: string;
  label?: string;
}

type CombinedInputProps = InputProps | TextareaProps;

export function Input({
  type = 'text',
  error,
  label,
  className,
  ...props
}: CombinedInputProps) {
  const baseClasses = cn(
    'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
    error && 'border-error-500 focus:ring-error-500 focus:border-error-500',
    className
  );

  const renderInput = () => {
    if (type === 'textarea') {
      const textareaProps = props as React.TextareaHTMLAttributes<HTMLTextAreaElement>;
      return (
        <textarea
          className={cn(baseClasses, 'min-h-[80px] resize-vertical')}
          {...textareaProps}
        />
      );
    }

    const inputProps = props as React.InputHTMLAttributes<HTMLInputElement>;
    return (
      <input
        type={type}
        className={baseClasses}
        {...inputProps}
      />
    );
  };

  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      {renderInput()}
      {error && (
        <p className="text-sm text-error-500">{error}</p>
      )}
    </div>
  );
}
