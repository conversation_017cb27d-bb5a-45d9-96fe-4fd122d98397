import React, { useState, useCallback, useRef, useEffect } from 'react';
import { cn } from '@/utils/cn';

interface MainLayoutProps {
  leftPanel: React.ReactNode;
  centerPanel: React.ReactNode;
  rightPanel: React.ReactNode;
  leftPanelCollapsed?: boolean;
  rightPanelCollapsed?: boolean;
  onToggleLeftPanel?: () => void;
  onToggleRightPanel?: () => void;
}

export function MainLayout({
  leftPanel,
  centerPanel,
  rightPanel,
  leftPanelCollapsed = false,
  rightPanelCollapsed = false,
}: MainLayoutProps) {
  // 最小和最大宽度限制
  const MIN_WIDTH = 300;
  const MAX_WIDTH = 800;
  const DEFAULT_WIDTH = 400;
  const STORAGE_KEY = 'mainLayout.rightPanelWidth';

  // 右侧面板宽度状态
  const [rightPanelWidth, setRightPanelWidth] = useState(() => {
    // 从localStorage读取保存的宽度
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const width = parseInt(saved, 10);
        if (width >= MIN_WIDTH && width <= MAX_WIDTH) {
          return width;
        }
      }
    }
    return DEFAULT_WIDTH;
  });

  const [isResizing, setIsResizing] = useState(false);
  const resizeRef = useRef<HTMLDivElement>(null);

  // 保存宽度到localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEY, rightPanelWidth.toString());
    }
  }, [rightPanelWidth]);

  // 双击重置宽度
  const handleDoubleClick = useCallback(() => {
    setRightPanelWidth(DEFAULT_WIDTH);
  }, []);

  // 开始拖拽
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);

    const startX = e.clientX;
    const startWidth = rightPanelWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = startX - e.clientX; // 向左拖拽为正值
      const newWidth = Math.min(MAX_WIDTH, Math.max(MIN_WIDTH, startWidth + deltaX));
      setRightPanelWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [rightPanelWidth]);

  return (
    <div className="h-screen w-screen flex bg-gray-50 overflow-hidden">
      {/* 左侧面板 - 工作区管理 */}
      <div
        className={cn(
          'bg-white border-r border-gray-200 transition-all duration-300 ease-in-out flex-shrink-0',
          leftPanelCollapsed ? 'w-0 overflow-hidden' : 'w-80'
        )}
      >
        {!leftPanelCollapsed && (
          <div className="h-full flex flex-col">
            {leftPanel}
          </div>
        )}
      </div>

      {/* 中央面板 - 文件查看器 */}
      <div className="flex-1 flex flex-col bg-white min-w-0 overflow-hidden">
        {centerPanel}
      </div>

      {/* 右侧面板 - 搜索对话 */}
      <div
        className={cn(
          'bg-white border-l border-gray-200 flex-shrink-0 relative',
          rightPanelCollapsed ? 'w-0 overflow-hidden' : ''
        )}
        style={{
          width: rightPanelCollapsed ? 0 : rightPanelWidth,
          transition: rightPanelCollapsed ? 'width 0.3s ease-in-out' : isResizing ? 'none' : 'none'
        }}
      >
        {/* 拖拽手柄 */}
        {!rightPanelCollapsed && (
          <div
            ref={resizeRef}
            className={cn(
              'absolute left-0 top-0 w-2 h-full cursor-col-resize z-10 group',
              'hover:bg-blue-500/20 transition-all duration-200',
              isResizing ? 'bg-blue-500/30' : 'bg-transparent'
            )}
            onMouseDown={handleMouseDown}
            onDoubleClick={handleDoubleClick}
            title="拖拽调整面板宽度，双击重置为默认宽度"
          >
            {/* 拖拽线 */}
            <div
              className={cn(
                'absolute left-0 top-0 w-0.5 h-full transition-all duration-200',
                'group-hover:bg-blue-500 group-hover:w-1',
                isResizing ? 'bg-blue-500 w-1' : 'bg-gray-300'
              )}
            />

            {/* 拖拽指示器 */}
            <div className={cn(
              'absolute left-0.5 top-1/2 transform -translate-y-1/2 transition-all duration-200',
              'w-1 h-8 bg-gray-400 rounded-r opacity-0',
              'group-hover:opacity-100 group-hover:bg-blue-500',
              isResizing ? 'opacity-100 bg-blue-500' : ''
            )}>
              {/* 拖拽点 */}
              <div className="absolute left-0.5 top-1/2 transform -translate-y-1/2 w-0.5 h-4 bg-white rounded-sm opacity-80" />
            </div>
          </div>
        )}

        {!rightPanelCollapsed && (
          <div className="h-full flex flex-col pl-1">
            {rightPanel}
          </div>
        )}
      </div>
    </div>
  );
}
