import React from 'react';
import { cn } from '@/utils/cn';

interface SidebarProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}

export function Sidebar({ title, children, className, actions }: SidebarProps) {
  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* 侧边栏标题 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-sm font-semibold text-gray-900">{title}</h2>
        {actions && (
          <div className="flex items-center gap-1">
            {actions}
          </div>
        )}
      </div>

      {/* 侧边栏内容 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {children}
      </div>
    </div>
  );
}

interface SidebarSectionProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  noPadding?: boolean;
}

export function SidebarSection({
  title,
  children,
  className,
  collapsible = false,
  defaultCollapsed = false,
  noPadding = false,
}: SidebarSectionProps) {
  const [collapsed, setCollapsed] = React.useState(defaultCollapsed);

  const isFlexSection = className?.includes('flex-1');

  return (
    <div className={cn(
      'border-b border-gray-200 last:border-b-0',
      isFlexSection && 'flex-1 flex flex-col min-h-0'
    )}>
      {title && (
        <div
          className={cn(
            'px-4 py-2 bg-gray-50 text-xs font-medium text-gray-700 uppercase tracking-wide',
            collapsible && 'cursor-pointer hover:bg-gray-100'
          )}
          onClick={collapsible ? () => setCollapsed(!collapsed) : undefined}
        >
          <div className="flex items-center justify-between">
            <span>{title}</span>
            {collapsible && (
              <span className={cn('transition-transform', collapsed && 'rotate-180')}>
                ▼
              </span>
            )}
          </div>
        </div>
      )}
      {(!collapsible || !collapsed) && (
        <div className={cn(
          noPadding ? '' : 'p-4',
          isFlexSection && 'flex-1 overflow-auto'
        )}>
          {children}
        </div>
      )}
    </div>
  );
}
