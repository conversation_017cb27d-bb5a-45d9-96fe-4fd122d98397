import { MainLayout, Header } from '@/components/layout';
import { WorkspaceSelector, FileTree, SettingsPanel } from '@/features/workspace/components';
import { FileViewer } from '@/features/file-viewer/components';
import { SearchPanel } from '@/features/search/components';
import { useAppStore } from '@/store/appStore';
import { useSearch } from '@/features/search/hooks';
import type { SearchRequest, CodeSnippet } from '@/types';

function App() {
  const {
    selectedFile,
    searchQuery,
    searchType,
    leftPanelCollapsed,
    rightPanelCollapsed,
    highlightLines,
    currentWorkspace,
    setSearchType,
    setSelectedFile,
    toggleLeftPanel,
    toggleRightPanel,
    clearSearch,
  } = useAppStore();

  const { executeSearch, clearResults, result, searchResultData, error, searchTime, loading, progressMessages } = useSearch();

  // 处理搜索请求
  const handleSearch = async (request: SearchRequest) => {
    try {
      // 开始新搜索前先清空之前的结果
      clearResults();
      await executeSearch(request);
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  // 处理清空搜索
  const handleClearSearch = () => {
    clearSearch();
    clearResults();
  };

  // 处理代码片段点击
  const handleSnippetClick = (snippet: CodeSnippet) => {
    // 打开对应的文件并高亮对应行
    const linesToHighlight = [];
    for (let i = snippet.start_line; i <= snippet.end_line; i++) {
      linesToHighlight.push(i + 1); // 转换为1-based行号
    }

    // 直接使用文件路径，getFileContent会自动拼接当前工作区
    setSelectedFile(snippet.file_path, linesToHighlight);
  };

  // 左侧面板内容
  const leftPanel = (
    <div className="h-full flex flex-col">
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-sm font-semibold text-gray-900">工作区</h2>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 工作区选择 - 固定高度 */}
        <div className="border-b border-gray-200">
          <div className="px-4 py-2 bg-gray-50 text-xs font-medium text-gray-700 uppercase tracking-wide">
            工作区选择
          </div>
          <div className="p-4">
            <WorkspaceSelector />
          </div>
        </div>

        {/* 文件浏览 - 可伸缩 */}
        <div className="flex-1 flex flex-col min-h-0 border-b border-gray-200">
          <div className="px-4 py-2 bg-gray-50 text-xs font-medium text-gray-700 uppercase tracking-wide">
            文件浏览
          </div>
          <div className="flex-1 overflow-auto">
            <FileTree />
          </div>
        </div>

        {/* 搜索设置 - 固定高度 */}
        <div>
          <div className="px-4 py-2 bg-gray-50 text-xs font-medium text-gray-700 uppercase tracking-wide">
            搜索设置
          </div>
          <div className="p-4">
            <SettingsPanel
              searchType={searchType}
              onSearchTypeChange={setSearchType}
            />
          </div>
        </div>
      </div>
    </div>
  );

  // 中央面板内容
  const centerPanel = (
    <div className="h-full flex flex-col">
      <Header
        leftPanelCollapsed={leftPanelCollapsed}
        rightPanelCollapsed={rightPanelCollapsed}
        onToggleLeftPanel={toggleLeftPanel}
        onToggleRightPanel={toggleRightPanel}
      />
      <div className="flex-1 overflow-hidden">
        <FileViewer
          filePath={selectedFile || undefined}
          searchTerm={searchQuery}
          highlightLines={highlightLines}
        />
      </div>
    </div>
  );

  // 右侧面板内容
  const rightPanel = (
    <div className="h-full flex flex-col">
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-sm font-semibold text-gray-900">搜索</h2>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 搜索查询 - 占用全部高度 */}
        <div className="flex-1 flex flex-col border-b border-gray-200 min-h-0">
          <div className="flex-shrink-0 px-4 py-2 bg-gray-50 text-xs font-medium text-gray-700 uppercase tracking-wide">
            搜索查询
          </div>
          <div className="flex-1 p-4 min-h-0">
            <SearchPanel
              onSearch={handleSearch}
              loading={loading}
              onClear={handleClearSearch}
              searchResult={result}
              searchResultData={searchResultData}
              searchError={error}
              searchTime={searchTime}
              progressMessages={progressMessages}
              onSnippetClick={handleSnippetClick}
            />
          </div>
        </div>

      </div>
    </div>
  );

  return (
    <MainLayout
      leftPanel={leftPanel}
      centerPanel={centerPanel}
      rightPanel={rightPanel}
      leftPanelCollapsed={leftPanelCollapsed}
      rightPanelCollapsed={rightPanelCollapsed}
    />
  );
}

export default App;
