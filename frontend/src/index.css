@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 自定义 CSS 变量 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #f9fafb;
  color: #111827;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 代码块样式 */
.code-block {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 文件树样式 */
.file-tree-item {
  @apply flex items-center px-2 py-1 text-sm hover:bg-gray-100 cursor-pointer rounded;
}

.file-tree-item.selected {
  @apply bg-primary-50 text-primary-700;
}

/* 搜索结果高亮 */
.search-highlight {
  @apply bg-yellow-200 px-1 rounded;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600;
}
