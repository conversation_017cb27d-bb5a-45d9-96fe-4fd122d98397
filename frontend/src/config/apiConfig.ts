/**
 * API配置文件
 * 管理前端对后端API的请求配置
 */

// API配置接口
export interface ApiConfig {
  /** 后端API基础路径 */
  baseUrl: string;
  /** API版本 */
  version: string;
  /** 请求超时时间（毫秒） */
  timeout: number;
}

// 默认配置
const defaultConfig: ApiConfig = {
  baseUrl: 'http://localhost:8000',
  version: 'v1',
  timeout: 30000,
};

// 环境配置映射
const envConfigs: Record<string, Partial<ApiConfig>> = {
  development: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || defaultConfig.baseUrl,
  },
  production: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || defaultConfig.baseUrl,
  },
  test: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || defaultConfig.baseUrl,
  },
};

// 获取当前环境
const getCurrentEnv = (): string => {
  return import.meta.env.MODE || 'development';
};

// 合并配置
const createConfig = (): ApiConfig => {
  const env = getCurrentEnv();
  const envConfig = envConfigs[env] || {};
  
  return {
    ...defaultConfig,
    ...envConfig,
  };
};

// 导出配置实例
export const apiConfig = createConfig();

// 导出API路径构建函数
export const buildApiUrl = (endpoint: string): string => {
  // 确保endpoint以/开头
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${apiConfig.baseUrl}${normalizedEndpoint}`;
};

// 导出常用的API端点
export const API_ENDPOINTS = {
  // 工作区相关
  WORKSPACES: 'workspaces',
  WORKSPACE_FILES: (workspaceId: string) => `/workspaces/${encodeURIComponent(workspaceId)}/files`,
  
  // 文件相关
  FILES: '/files',
  
  // 搜索相关
  SEARCH: '/search',
} as const;

// 导出配置对象，供其他模块使用
export default apiConfig;
