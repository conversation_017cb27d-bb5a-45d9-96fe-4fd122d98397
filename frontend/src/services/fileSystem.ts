import type { FileNode, WorkspaceOption } from "@/types";
import { buildApiUrl, API_ENDPOINTS } from "@/config/apiConfig";

// 文件系统服务，通过 API 调用读取真实的文件系统数据
export class FileSystemService {
  private static instance: FileSystemService;

  static getInstance(): FileSystemService {
    if (!FileSystemService.instance) {
      FileSystemService.instance = new FileSystemService();
    }
    return FileSystemService.instance;
  }

  // 获取可用的工作区列表
  async getWorkspaces(): Promise<WorkspaceOption[]> {
    try {
      const response = await fetch(buildApiUrl(API_ENDPOINTS.WORKSPACES));
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const workspaces = await response.json();
      return workspaces;
    } catch (error) {
      console.error("Failed to get workspaces:", error);
      return [];
    }
  }

  // 获取文件树结构
  async getFileTree(workspacePath: string): Promise<FileNode[]> {
    try {
      // 从路径中提取工作区名称
      const workspaceName = workspacePath.split("/").pop() || "";

      const url = buildApiUrl(API_ENDPOINTS.WORKSPACE_FILES(workspaceName));

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const fileTree = await response.json();

      return fileTree;
    } catch (error) {
      console.error("Failed to get file tree:", error);
      return [];
    }
  }

  // 获取文件内容
  async getFileContent(filePath: string, currentWorkspace?: string): Promise<string> {
    try {
      // 如果提供了当前工作区且文件路径不是以工作区开头，则自动拼接
      let fullFilePath = filePath;
      if (currentWorkspace && !filePath.startsWith(currentWorkspace + '/')) {
        fullFilePath = `${currentWorkspace}/${filePath}`;
      }

      const response = await fetch(`${buildApiUrl(API_ENDPOINTS.FILES)}?file_path=${encodeURIComponent(fullFilePath)}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.content || "";
    } catch (error) {
      console.error("Failed to get file content:", error);
      return "";
    }
  }
}

export const fileSystemService = FileSystemService.getInstance();
