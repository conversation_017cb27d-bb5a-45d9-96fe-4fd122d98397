import { useState, useEffect } from 'react';
import { fileSystemService } from '@/services/fileSystem';
import { useAppStore } from '@/store/appStore';
import type { FileNode } from '@/types';

export function useFileTree(workspacePath: string | null) {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    fileTree,
    selectedFile,
    currentWorkspace,
    setFileTree
  } = useAppStore();

  // 加载文件树
  // 文件排序函数：目录在前，文件在后，然后按名称字母顺序排序
  const sortFileNodes = (nodes: FileNode[]): FileNode[] => {
    return nodes.sort((a, b) => {
      // 首先按类型排序：目录在前，文件在后
      if (a.type !== b.type) {
        if (a.type === 'directory') return -1;
        if (b.type === 'directory') return 1;
      }

      // 然后按名称字母顺序排序（不区分大小写）
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });
  };

  const loadFileTree = async (workspacePath: string) => {
    setLoading(true);
    setError(null);

    try {
      const tree = await fileSystemService.getFileTree(workspacePath);
      // 对文件树进行排序
      const sortedTree = sortFileNodes(tree);
      setFileTree(sortedTree);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载文件树失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换文件夹展开状态
  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };



  // 递归查找文件节点
  const findFileNode = (nodes: FileNode[], filePath: string): FileNode | null => {
    for (const node of nodes) {
      if (node.path === filePath) {
        return node;
      }
      if (node.children) {
        const found = findFileNode(node.children, filePath);
        if (found) return found;
      }
    }
    return null;
  };

  // 获取选中的文件节点
  const getSelectedFileNode = () => {
    if (!selectedFile) return null;
    return findFileNode(fileTree, selectedFile);
  };

  // 展开到指定文件的路径
  const expandToFile = (filePath: string) => {
    const pathParts = filePath.split('/');
    const newExpanded = new Set(expandedFolders);
    
    let currentPath = '';
    for (let i = 0; i < pathParts.length - 1; i++) {
      currentPath += (i > 0 ? '/' : '') + pathParts[i];
      newExpanded.add(currentPath);
    }
    
    setExpandedFolders(newExpanded);
  };

  // 当工作区变化时重新加载文件树
  useEffect(() => {
    console.log('useFileTree effect triggered, currentWorkspace:', currentWorkspace);
    console.log('useFileTree effect - workspacePath:', workspacePath);
    console.log('useFileTree effect - typeof workspacePath:', typeof workspacePath);
    console.log('useFileTree effect - workspacePath === null:', workspacePath === null);
    console.log('useFileTree effect - workspacePath === undefined:', workspacePath === undefined);

    if (workspacePath && currentWorkspace) {
      console.log('Loading file tree for workspace:', workspacePath);
      // 立即清空当前文件树，显示加载状态
      setFileTree([]);
      setExpandedFolders(new Set());
      loadFileTree(workspacePath);
    } else {
      // 如果没有工作区，清空文件树
      console.log('No workspace path, clearing file tree');
      setFileTree([]);
      setExpandedFolders(new Set());
    }
  }, [currentWorkspace, workspacePath]);

  return {
    fileTree,
    expandedFolders,
    selectedFile,
    loading,
    error,
    toggleFolder,
    getSelectedFileNode,
    expandToFile,
    reload: () => workspacePath && loadFileTree(workspacePath),
  };
}
