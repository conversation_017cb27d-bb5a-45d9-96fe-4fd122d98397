import { useState, useEffect, useMemo } from 'react';
import { fileSystemService } from '@/services/fileSystem';
import { useAppStore } from '@/store/appStore';

export function useWorkspace() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    workspaces,
    currentWorkspace,
    setWorkspaces,
    setCurrentWorkspace
  } = useAppStore();

  // 加载工作区列表
  const loadWorkspaces = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const workspaceList = await fileSystemService.getWorkspaces();
      setWorkspaces(workspaceList);
      
      // 如果没有选中的工作区且有可用工作区，选择第一个
      if (!currentWorkspace && workspaceList.length > 0) {
        console.log('Auto-selecting first workspace:', workspaceList[0].id);
        setCurrentWorkspace(workspaceList[0].id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载工作区失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换工作区
  const switchWorkspace = (workspaceId: string) => {
    console.log('Switching workspace from', currentWorkspace, 'to:', workspaceId);
    console.log('Available workspaces:', workspaces);

    // setCurrentWorkspace 会自动清空 selectedFile, fileContent, fileTree
    setCurrentWorkspace(workspaceId);
    console.log('Workspace switched, new currentWorkspace:', workspaceId);

    // 立即检查新的工作区信息
    const newWorkspace = workspaces.find(ws => ws.id === workspaceId);
    console.log('New workspace info:', newWorkspace);
  };

  // 获取当前工作区信息
  const getCurrentWorkspace = useMemo(() => {
    return workspaces.find(ws => ws.id === currentWorkspace);
  }, [workspaces, currentWorkspace]);

  useEffect(() => {
    loadWorkspaces();
  }, []);

  // 监听当前工作区变化
  useEffect(() => {
    console.log('useWorkspace: currentWorkspace changed to:', currentWorkspace);
    const workspace = workspaces.find(ws => ws.id === currentWorkspace);
    console.log('useWorkspace: found workspace:', workspace);
  }, [currentWorkspace, workspaces]);

  return {
    workspaces,
    currentWorkspace,
    loading,
    error,
    switchWorkspace,
    getCurrentWorkspace,
    reload: loadWorkspaces,
  };
}
