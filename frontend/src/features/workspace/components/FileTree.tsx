import React from 'react';
import { cn } from '@/utils/cn';
import { Loading } from '@/components/ui';
import { 
  Folder, 
  FolderOpen, 
  File, 
  FileText, 
  FileCode, 
  FileImage,
  FileVideo,
  FileArchive,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { useFileTree } from '../hooks/useFileTree';
import { useWorkspace } from '../hooks/useWorkspace';
import { useAppStore } from '@/store/appStore';
import { getFileExtension } from '@/utils/format';
import type { FileNode } from '@/types';

// 根据文件扩展名获取图标
function getFileIcon(fileName: string) {
  const extension = getFileExtension(fileName).toLowerCase();
  
  const iconMap: Record<string, React.ComponentType<any>> = {
    // 代码文件
    js: FileCode,
    jsx: FileCode,
    ts: FileCode,
    tsx: FileCode,
    py: FileCode,
    java: FileCode,
    cpp: FileCode,
    c: FileCode,
    cs: FileCode,
    php: FileCode,
    rb: FileCode,
    go: FileCode,
    rs: FileCode,
    swift: FileCode,
    kt: FileCode,
    scala: FileCode,
    html: FileCode,
    css: FileCode,
    scss: FileCode,
    sass: FileCode,
    less: FileCode,
    
    // 文档文件
    md: FileText,
    txt: FileText,
    doc: FileText,
    docx: FileText,
    pdf: FileText,
    
    // 图片文件
    jpg: FileImage,
    jpeg: FileImage,
    png: FileImage,
    gif: FileImage,
    svg: FileImage,
    webp: FileImage,
    
    // 视频文件
    mp4: FileVideo,
    avi: FileVideo,
    mov: FileVideo,
    wmv: FileVideo,
    
    // 压缩文件
    zip: FileArchive,
    rar: FileArchive,
    tar: FileArchive,
    gz: FileArchive,
  };
  
  return iconMap[extension] || File;
}

interface FileTreeNodeProps {
  node: FileNode;
  level: number;
  expandedFolders: Set<string>;
  selectedFile: string | null;
  onToggle: (nodeId: string) => void;
  onSelect: (filePath: string) => void;
}

function FileTreeNode({
  node,
  level,
  expandedFolders,
  selectedFile,
  onToggle,
  onSelect
}: FileTreeNodeProps) {
  const isExpanded = expandedFolders.has(node.id);
  const isSelected = selectedFile === node.path;

  const IconComponent = node.type === 'directory'
    ? (isExpanded ? FolderOpen : Folder)
    : getFileIcon(node.name);

  const handleClick = () => {
    if (node.type === 'directory') {
      onToggle(node.id);
    } else {
      onSelect(node.path);
    }
  };

  return (
    <div>
      <div
        className={cn(
          'flex items-center gap-1 px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 rounded',
          isSelected && 'bg-primary-50 text-primary-700',
          'transition-colors'
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
      >
        {node.type === 'directory' && (
          <span className="flex-shrink-0">
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </span>
        )}
        
        <IconComponent className={cn(
          'h-4 w-4 flex-shrink-0',
          node.type === 'directory' ? 'text-blue-500' : 'text-gray-500'
        )} />
        
        <span className="truncate">{node.name}</span>
      </div>

      {node.type === 'directory' && isExpanded && node.children && (
        <div>
          {node.children.map(child => (
            <FileTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              expandedFolders={expandedFolders}
              selectedFile={selectedFile}
              onToggle={onToggle}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export function FileTree() {
  const { workspaces, currentWorkspace: currentWorkspaceId } = useWorkspace();
  const { setSelectedFile } = useAppStore();

  // 直接计算当前工作区，避免 memoization 问题
  const currentWorkspace = workspaces.find(ws => ws.id === currentWorkspaceId);

  console.log('FileTree render - currentWorkspaceId:', currentWorkspaceId);
  console.log('FileTree render - workspaces:', workspaces);
  console.log('FileTree render - currentWorkspace:', currentWorkspace);
  console.log('FileTree render - workspace path:', currentWorkspace?.path);

  const {
    fileTree,
    expandedFolders,
    selectedFile,
    loading,
    error,
    toggleFolder,
  } = useFileTree(currentWorkspace?.path || null);

  // 处理文件选择
  const handleFileSelect = (filePath: string) => {
    setSelectedFile(filePath);
  };

  if (!currentWorkspace) {
    return (
      <div className="flex-1 overflow-auto">
        <div className="p-4 text-center text-gray-500">
          请先选择一个工作区
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex-1 overflow-auto">
        <div className="p-4">
          <Loading text="加载文件树..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 overflow-auto">
        <div className="p-4 text-sm text-error-500">
          加载文件树失败: {error}
        </div>
      </div>
    );
  }

  if (fileTree.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        该工作区没有文件
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto">
      <div className="p-2">
        {fileTree.map(node => (
          <FileTreeNode
            key={node.id}
            node={node}
            level={0}
            expandedFolders={expandedFolders}
            selectedFile={selectedFile}
            onToggle={toggleFolder}
            onSelect={handleFileSelect}
          />
        ))}
      </div>
    </div>
  );
}
