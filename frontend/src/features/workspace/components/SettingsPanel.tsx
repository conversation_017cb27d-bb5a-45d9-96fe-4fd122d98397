import { Select } from '@/components/ui';

interface SettingsPanelProps {
  searchType: 'grep' | 'term_sparse' | 'embedding' | 'inverted_index' | 'any';
  onSearchTypeChange: (type: 'grep' | 'term_sparse' | 'embedding' | 'inverted_index' | 'any') => void;
}

export function SettingsPanel({ searchType, onSearchTypeChange }: SettingsPanelProps) {
  const searchTypeOptions = [
    { label: 'Grep 搜索', value: 'grep' as const },
    { label: 'Term Sparse 搜索', value: 'term_sparse' as const },
    { label: 'Embedding 搜索', value: 'embedding' as const },
    { label: '倒排索引搜索', value: 'inverted_index' as const },
    { label: '自动搜索', value: 'any' as const },
  ];


  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <Select
          options={searchTypeOptions}
          value={searchType}
          onChange={onSearchTypeChange}
        />
      </div>
    </div>
  );
}
