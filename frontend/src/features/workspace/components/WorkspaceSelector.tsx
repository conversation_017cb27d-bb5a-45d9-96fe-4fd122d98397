import { Select, Loading } from '@/components/ui';
import { useWorkspace } from '../hooks/useWorkspace';

export function WorkspaceSelector() {
  const { workspaces, currentWorkspace, loading, error, switchWorkspace } = useWorkspace();

  const options = workspaces.map(workspace => ({
    label: workspace.name,
    value: workspace.id,
  }));

  if (loading) {
    return (
      <div className="p-4">
        <Loading text="加载工作区..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="text-sm text-error-500">
          加载工作区失败: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <Select
        options={options}
        value={currentWorkspace || ''}
        onChange={switchWorkspace}
        placeholder="请选择工作区..."
        searchable
      />
      
    </div>
  );
}
