import { useState, useCallback } from 'react';
import type { SearchProcessStep } from '@/types';

export function useSearchProcess() {
  const [processSteps, setProcessSteps] = useState<SearchProcessStep[]>([]);
  const [currentStep, setCurrentStep] = useState<number>(-1);

  // 更新搜索过程
  const updateProcess = useCallback((steps: SearchProcessStep[]) => {
    setProcessSteps(steps);
    
    // 找到当前正在执行的步骤
    const runningStepIndex = steps.findIndex(step => step.status === 'running');
    const completedSteps = steps.filter(step => step.status === 'completed').length;
    
    if (runningStepIndex !== -1) {
      setCurrentStep(runningStepIndex);
    } else if (completedSteps === steps.length) {
      setCurrentStep(steps.length - 1);
    }
  }, []);

  // 清空搜索过程
  const clearProcess = useCallback(() => {
    setProcessSteps([]);
    setCurrentStep(-1);
  }, []);

  // 添加新的步骤
  const addStep = useCallback((step: SearchProcessStep) => {
    setProcessSteps(prev => [...prev, step]);
  }, []);

  // 更新步骤状态
  const updateStepStatus = useCallback((stepId: string, status: SearchProcessStep['status'], details?: string) => {
    setProcessSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { 
            ...step, 
            status, 
            details: details || step.details,
            endTime: status === 'completed' || status === 'error' ? new Date() : step.endTime
          }
        : step
    ));
  }, []);

  return {
    processSteps,
    currentStep,
    updateProcess,
    clearProcess,
    addStep,
    updateStepStatus,
  };
}
