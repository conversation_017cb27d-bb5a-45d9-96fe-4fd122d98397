import { useState, useCallback } from 'react';
import { flushSync } from 'react-dom';
import { searchService } from '@/services/searchService';
import type { SearchRequest, SearchResultData } from '@/types';

interface ProgressMessage {
  message: string;
  timestamp: number;
}

export function useSearch() {
  const [result, setResult] = useState<string>('');
  const [searchResultData, setSearchResultData] = useState<SearchResultData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastQuery, setLastQuery] = useState<string>('');
  const [searchTime, setSearchTime] = useState<number>(0);
  const [progressMessages, setProgressMessages] = useState<ProgressMessage[]>([]);

  // 执行流式搜索（显示过程）
  const executeSearch = useCallback(async (request: SearchRequest) => {
    setLoading(true);
    setError(null);
    setLastQuery(request.query);
    setProgressMessages([]);
    setResult('');

    const startTime = Date.now();

    try {
      await searchService.executeSearch(
        request,
        // onProgress
        (message: string) => {
          // 使用flushSync强制同步更新，确保UI及时响应
          flushSync(() => {
            setProgressMessages(prev => [...prev, { message, timestamp: Date.now() }]);
          });
        },
        // onComplete
        (finalResult: string, resultData?: SearchResultData) => {
          // 使用flushSync确保完成状态立即更新
          flushSync(() => {
            setResult(finalResult); // 保持空白，不显示结果
            setSearchResultData(resultData || null);
            setSearchTime(Date.now() - startTime);
            setLoading(false); // 重要：停止加载状态
          });
        },
        // onError
        (errorMsg: string) => {
          setError(errorMsg);
          setLoading(false);
        }
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败');
      setLoading(false);
    }
  }, []);

  // 清空搜索结果
  const clearResults = useCallback(() => {
    setResult('');
    setSearchResultData(null);
    setError(null);
    setLastQuery('');
    setSearchTime(0);
    setProgressMessages([]);
  }, []);

  return {
    result,
    searchResultData,
    loading,
    error,
    lastQuery,
    searchTime,
    progressMessages,
    executeSearch,
    clearResults,
  };
}
