import React from 'react';
import { File, Hash } from 'lucide-react';
import type { CodeSnippet } from '@/types';
import { cn } from '@/utils/cn';

interface SearchResultCardProps {
  snippet: CodeSnippet;
  onClick?: (snippet: CodeSnippet) => void;
  className?: string;
}

export function SearchResultCard({
  snippet,
  onClick,
  className
}: SearchResultCardProps) {
  const handleClick = () => {
    onClick?.(snippet);
  };

  // 获取文件名
  const fileName = snippet.file_path.split('/').pop() || snippet.file_path;
  
  // 获取代码预览（限制行数）
  const codeLines = snippet.content.split('\n');
  const previewLines = codeLines.slice(0, 5); // 最多显示5行
  const hasMoreLines = codeLines.length > 5;

  // 格式化分数
  const formattedScore = snippet.score.toFixed(2);

  return (
    <div
      className={cn(
        'bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer',
        className
      )}
      onClick={handleClick}
    >
      {/* 文件信息头部 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <File className="h-4 w-4 text-gray-500 flex-shrink-0" />
          <span className="text-sm font-medium text-gray-900 truncate" title={snippet.file_path}>
            {fileName}
          </span>
          <span className="text-xs text-gray-500 flex-shrink-0">
            {snippet.file_path}
          </span>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Hash className="h-3 w-3" />
            <span>{snippet.start_line + 1}-{snippet.end_line + 1}</span>
          </div>
          <div className="text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded">
            {formattedScore}
          </div>
        </div>
      </div>

      {/* 代码预览 */}
      <div className="bg-gray-50 rounded-md p-3 overflow-hidden">
        <pre className="text-sm text-gray-800 font-mono leading-relaxed whitespace-pre-wrap">
          {previewLines.join('\n')}
          {hasMoreLines && (
            <span className="text-gray-500 italic">
              \n... ({codeLines.length - 5} more lines)
            </span>
          )}
        </pre>
      </div>

      {/* 上下文信息 */}
      {(snippet.context_before || snippet.context_after) && (
        <div className="mt-3 text-xs text-gray-500">
          <span>包含上下文</span>
          {snippet.context_before && (
            <span className="ml-2">前置: {snippet.context_before.split('\n').length} 行</span>
          )}
          {snippet.context_after && (
            <span className="ml-2">后置: {snippet.context_after.split('\n').length} 行</span>
          )}
        </div>
      )}
    </div>
  );
}
