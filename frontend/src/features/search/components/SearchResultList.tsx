import React from 'react';
import { SearchResultCard } from './SearchResultCard';
import type { SearchResultData, CodeSnippet } from '@/types';
import { cn } from '@/utils/cn';

interface SearchResultListProps {
  searchResult: SearchResultData;
  onSnippetClick?: (snippet: CodeSnippet) => void;
  className?: string;
}

export function SearchResultList({
  searchResult,
  onSnippetClick,
  className
}: SearchResultListProps) {
  const { code_snippets, total_snippets, total_files } = searchResult;

  if (!code_snippets || code_snippets.length === 0) {
    return (
      <div className={cn('text-center py-8', className)}>
        <div className="text-gray-500 text-sm">
          未找到相关代码片段
        </div>
      </div>
    );
  }

  // 按文件分组
  const groupedByFile = code_snippets.reduce((groups, snippet) => {
    const filePath = snippet.file_path;
    if (!groups[filePath]) {
      groups[filePath] = [];
    }
    groups[filePath].push(snippet);
    return groups;
  }, {} as Record<string, CodeSnippet[]>);

  return (
    <div className={cn('space-y-4', className)}>
      {/* 统计信息 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="text-sm text-blue-800">
          <span className="font-medium">搜索结果统计:</span>
          <span className="ml-2">共找到 {total_snippets} 个代码片段</span>
          <span className="ml-2">涉及 {total_files} 个文件</span>
        </div>
      </div>

      {/* 按文件分组展示结果 */}
      {Object.entries(groupedByFile).map(([filePath, snippets]) => (
        <div key={filePath} className="space-y-3">
          {/* 文件标题 */}
          <div className="flex items-center gap-2 py-2 border-b border-gray-200">
            <h4 className="text-sm font-semibold text-gray-900">
              📁 {filePath}
            </h4>
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {snippets.length} 个片段
            </span>
          </div>

          {/* 该文件的所有代码片段 */}
          <div className="space-y-3 pl-4">
            {snippets.map((snippet, index) => (
              <SearchResultCard
                key={`${filePath}-${snippet.start_line}-${index}`}
                snippet={snippet}
                onClick={onSnippetClick}
                className="border-l-2 border-blue-200"
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
