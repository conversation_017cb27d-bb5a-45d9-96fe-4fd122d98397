import React, { useMemo } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@/utils/cn';
import { Loading } from '@/components/ui';

interface CodeViewerProps {
  content: string;
  language: string;
  filePath?: string;
  highlightLines?: number[];
  searchTerm?: string;
  lineNumbers?: boolean;
  readOnly?: boolean;
  loading?: boolean;
  className?: string;
}

export function CodeViewer({
  content,
  language,
  filePath: _filePath, // 重命名为 _filePath 表示未使用
  highlightLines = [],
  searchTerm,
  lineNumbers = true,
  readOnly: _readOnly = true, // 重命名为 _readOnly 表示未使用
  loading = false,
  className,
}: CodeViewerProps) {
  // 处理搜索关键词高亮
  const processedContent = useMemo(() => {
    if (!searchTerm || !content) return content;
    
    // 简单的关键词高亮处理
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return content.replace(regex, '**$1**'); // 使用 markdown 语法标记
  }, [content, searchTerm]);

  // 自定义样式，支持行高亮
  const customStyle = useMemo(() => {
    const baseStyle = {
      ...vscDarkPlus,
      'pre[class*="language-"]': {
        ...vscDarkPlus['pre[class*="language-"]'],
        margin: 0,
        padding: '1rem',
        fontSize: '14px',
        lineHeight: '1.5',
        fontFamily: '"JetBrains Mono", "Fira Code", monospace',
      },
    };

    // 添加行高亮样式
    if (highlightLines.length > 0) {
      highlightLines.forEach(lineNumber => {
        (baseStyle as any)[`.line-${lineNumber}`] = {
          backgroundColor: 'rgba(255, 255, 0, 0.1)',
          display: 'block',
          margin: '0 -1rem',
          padding: '0 1rem',
        };
      });
    }

    return baseStyle;
  }, [highlightLines]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading text="加载文件内容..." />
      </div>
    );
  }

  if (!content) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <div className="text-lg mb-2">📄</div>
          <div>请选择一个文件查看内容</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('h-full overflow-auto', className)}>
      <SyntaxHighlighter
        language={language}
        style={customStyle}
        showLineNumbers={lineNumbers}
        wrapLines={true}
        lineProps={(lineNumber) => {
          const style: React.CSSProperties = {};
          
          // 高亮指定行
          if (highlightLines.includes(lineNumber)) {
            style.backgroundColor = 'rgba(255, 255, 0, 0.1)';
            style.display = 'block';
            style.margin = '0 -1rem';
            style.padding = '0 1rem';
          }
          
          return { style };
        }}
        customStyle={{
          margin: 0,
          padding: 0,
          background: 'transparent',
        }}
        codeTagProps={{
          style: {
            fontFamily: '"JetBrains Mono", "Fira Code", monospace',
            fontSize: '14px',
          },
        }}
      >
        {processedContent}
      </SyntaxHighlighter>
    </div>
  );
}
