import { cn } from '@/utils/cn';
import { CodeViewer } from './CodeViewer';
import { useFileContent } from '../hooks/useFileContent';

interface FileViewerProps {
  filePath?: string;
  searchTerm?: string;
  highlightLines?: number[];
  className?: string;
}

export function FileViewer({
  filePath,
  searchTerm,
  highlightLines,
  className
}: FileViewerProps) {
  const { content, language, loading, error, reload } = useFileContent(filePath);

  if (error) {
    return (
      <div className={cn('h-full flex items-center justify-center', className)}>
        <div className="text-center">
          <div className="text-error-500 text-lg mb-2">⚠️</div>
          <div className="text-error-500 text-sm">
            加载文件失败: {error}
          </div>
          <button
            onClick={reload}
            className="mt-2 text-primary-600 hover:text-primary-700 text-sm underline"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('h-full flex flex-col bg-white', className)}>
      {/* 代码查看器 */}
      <div className="flex-1 overflow-hidden">
        <CodeViewer
          content={content}
          language={language}
          filePath={filePath}
          searchTerm={searchTerm}
          highlightLines={highlightLines}
          loading={loading}
          className="h-full"
        />
      </div>
    </div>
  );
}
