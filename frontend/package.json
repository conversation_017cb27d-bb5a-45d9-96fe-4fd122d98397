{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.536.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^24.2.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}