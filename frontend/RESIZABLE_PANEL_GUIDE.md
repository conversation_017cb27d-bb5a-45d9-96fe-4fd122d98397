# 可调整大小的右侧面板功能

## 功能概述

MainLayout组件现在支持通过拖拽调整右侧面板的宽度，提供更好的用户体验和界面自定义能力。

## 新增功能

### 1. 拖拽调整宽度
- **操作方式**: 将鼠标悬停在右侧面板的左边缘，会出现一个可拖拽的手柄
- **视觉反馈**: 
  - 鼠标悬停时手柄会高亮显示（蓝色）
  - 拖拽时手柄保持高亮状态
  - 鼠标光标会变为调整大小的图标

### 2. 宽度限制
- **最小宽度**: 300px
- **最大宽度**: 800px
- **默认宽度**: 400px

### 3. 双击重置
- **操作方式**: 双击拖拽手柄
- **功能**: 快速重置面板宽度为默认值（400px）

### 4. 持久化存储
- **自动保存**: 用户调整的宽度会自动保存到localStorage
- **自动恢复**: 页面刷新后会自动恢复用户上次设置的宽度
- **存储键**: `mainLayout.rightPanelWidth`

## 技术实现

### 核心特性
1. **React Hooks**: 使用useState、useCallback、useRef、useEffect
2. **事件处理**: 鼠标拖拽事件的完整生命周期管理
3. **样式动态**: 基于状态的动态CSS类和内联样式
4. **本地存储**: localStorage集成用于持久化用户偏好

### 拖拽手柄设计
```tsx
{/* 拖拽手柄 */}
<div className="absolute left-0 top-0 w-2 h-full cursor-col-resize z-10 group">
  {/* 拖拽线 */}
  <div className="absolute left-0 top-0 w-0.5 h-full" />
  
  {/* 拖拽指示器 */}
  <div className="absolute left-0.5 top-1/2 transform -translate-y-1/2">
    {/* 拖拽点 */}
    <div className="w-0.5 h-4 bg-white rounded-sm" />
  </div>
</div>
```

### 状态管理
```tsx
const [rightPanelWidth, setRightPanelWidth] = useState(() => {
  // 从localStorage读取保存的宽度
  const saved = localStorage.getItem(STORAGE_KEY);
  return saved ? parseInt(saved, 10) : DEFAULT_WIDTH;
});

const [isResizing, setIsResizing] = useState(false);
```

## 用户体验改进

### 视觉反馈
- **悬停效果**: 手柄在鼠标悬停时会显示蓝色高亮
- **拖拽状态**: 拖拽时手柄保持高亮，提供清晰的视觉反馈
- **平滑过渡**: 所有状态变化都有平滑的CSS过渡效果

### 交互优化
- **防止文本选择**: 拖拽时禁用文本选择，避免意外选中
- **全局光标**: 拖拽时设置全局光标为调整大小图标
- **事件清理**: 完善的事件监听器清理，避免内存泄漏

### 响应式设计
- **最小宽度保护**: 确保面板不会变得太小而影响可用性
- **最大宽度限制**: 防止面板占用过多屏幕空间
- **折叠状态兼容**: 与现有的面板折叠功能完全兼容

## 使用示例

```tsx
<MainLayout
  leftPanel={<WorkspacePanel />}
  centerPanel={<FileViewer />}
  rightPanel={<SearchPanel />}
  leftPanelCollapsed={leftCollapsed}
  rightPanelCollapsed={rightCollapsed}
/>
```

用户可以：
1. 拖拽右侧面板边缘调整宽度
2. 双击边缘快速重置为默认宽度
3. 设置会自动保存并在下次访问时恢复

## 浏览器兼容性

- ✅ Chrome/Edge (现代版本)
- ✅ Firefox (现代版本)
- ✅ Safari (现代版本)
- ✅ 支持localStorage的所有现代浏览器

## 注意事项

1. **SSR兼容**: 代码包含了服务端渲染的兼容性检查
2. **性能优化**: 使用useCallback避免不必要的重渲染
3. **内存管理**: 正确清理事件监听器，防止内存泄漏
4. **类型安全**: 完整的TypeScript类型定义
