# 多阶段构建 Dockerfile for React + TypeScript + Vite 前端项目
# 支持多架构构建（AMD64/ARM64）

# 第一阶段：构建阶段
FROM --platform=$BUILDPLATFORM node:20-alpine AS builder

# 设置构建参数
ARG TARGETPLATFORM
ARG BUILDPLATFORM

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=development
ENV VITE_BUILD_MODE=production
ENV NPM_CONFIG_CACHE=/tmp/.npm
ENV NPM_CONFIG_UPDATE_NOTIFIER=false

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# 复制 package.json 和 package-lock.json
COPY frontend/package*.json ./

# 安装依赖并处理架构兼容性问题
RUN npm cache clean --force && \
    rm -rf node_modules package-lock.json && \
    npm install --no-optional --legacy-peer-deps && \
    npm rebuild

# 复制源代码
COPY frontend/ ./

# 构建应用
RUN npm run build

# 验证构建产物
RUN ls -la dist/

# 第二阶段：生产阶段
FROM nginx:1.25-alpine AS production

# 安装必要的工具
RUN apk add --no-cache \
    curl \
    tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 复制构建产物到 nginx 目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 nginx 配置文件
COPY deploy/nginx.conf /etc/nginx/nginx.conf

# 创建必要的目录并设置权限
RUN mkdir -p /var/log/nginx /var/cache/nginx/client_temp /var/cache/nginx/proxy_temp \
    /var/cache/nginx/fastcgi_temp /var/cache/nginx/uwsgi_temp /var/cache/nginx/scgi_temp && \
    chown -R nextjs:nodejs /var/log/nginx && \
    chown -R nextjs:nodejs /usr/share/nginx/html && \
    chown -R nextjs:nodejs /var/cache/nginx && \
    chown -R nextjs:nodejs /etc/nginx && \
    chmod -R 755 /var/cache/nginx

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 切换到非 root 用户
USER nextjs

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
