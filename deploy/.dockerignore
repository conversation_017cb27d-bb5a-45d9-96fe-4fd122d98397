# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 构建输出
dist/
build/
.next/
out/

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log

# 测试
coverage/
.nyc_output/
.jest/

# 缓存
.cache/
.parcel-cache/
.eslintcache

# 临时文件
tmp/
temp/

# 文档
README.md
CHANGELOG.md
LICENSE
*.md

# 其他项目文件
backend/
deploy/*.md
.dockerignore
Dockerfile*
docker-compose*.yml

# 虚拟环境
.venv
